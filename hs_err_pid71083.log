#
# A fatal error has been detected by the Java Runtime Environment:
#
#  SIGBUS (0xa) at pc=0x00000001009ebc2c, pid=71083, tid=39683
#
# JRE version: OpenJDK Runtime Environment Corretto-*********.1 (17.0.15+6) (build 17.0.15+6-LTS)
# Java VM: OpenJDK 64-Bit Server VM Corretto-*********.1 (17.0.15+6-LTS, mixed mode, emulated-client, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, bsd-aarch64)
# Problematic frame:
# C  [libzip.dylib+0x13c2c]  newEntry+0x68
#
# No core dump will be written. Core dumps have been disabled. To enable core dumping, try "ulimit -c unlimited" before starting Java again
#
# If you would like to submit a bug report, please visit:
#   https://github.com/corretto/corretto-17/issues/
# The crash happened outside the Java Virtual Machine in native code.
# See problematic frame for where to report the bug.
#

---------------  S U M M A R Y ------------

Command Line: -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:57629,suspend=y,server=n -javaagent:/Users/<USER>/Library/Caches/JetBrains/IntelliJIdea2025.1/captureAgent/debugger-agent.jar=file:///var/folders/83/lx4p4bhs5bqg28rs3x6_qp600000gn/T/capture6868567008683401803.props --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.time=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.math=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.security=ALL-UNNAMED --add-opens=java.base/java.text=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.base/java.util.regex=ALL-UNNAMED --add-opens=java.sql/java.sql=ALL-UNNAMED -agentpath:/private/var/folders/83/lx4p4bhs5bqg28rs3x6_qp600000gn/T/idea_libasyncProfiler_dylib_temp_folder/libasyncProfiler.dylib=version,jfr,event=wall,interval=10ms,cstack=no,file=/Users/<USER>/IdeaSnapshots/DelayedServiceApplication_2025_07_31_134249.jfr,log=/private/var/folders/83/lx4p4bhs5bqg28rs3x6_qp600000gn/T/DelayedServiceApplication_2025_07_31_134249.jfr.log.txt,logLevel=DEBUG -XX:TieredStopAtLevel=1 -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true -Dmanagement.endpoints.jmx.exposure.include=* -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Ddebugger.agent.enable.coroutines=true -Dkotlinx.coroutines.debug.enable.flows.stack.trace=true -Dkotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true -Dfile.encoding=UTF-8 com.knet.delayed.DelayedServiceApplication

Host: "MacBookPro18,1" arm64, 10 cores, 16G, Darwin 24.5.0, macOS 15.5 (24F74)
Time: Thu Jul 31 13:42:50 2025 CST elapsed time: 0.859700 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x0000000131bb6400):  JavaThread "spring.cloud.inetutils" daemon [_thread_in_native, id=39683, stack(0x000000030353c000,0x000000030373f000)]

Stack: [0x000000030353c000,0x000000030373f000],  sp=0x000000030373dfd0,  free space=2055k
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
C  [libzip.dylib+0x13c2c]  newEntry+0x68
C  [libzip.dylib+0x13b00]  ZIP_GetEntry2+0x14c
C  [libzip.dylib+0x14540]  ZIP_FindEntry+0x3c
V  [libjvm.dylib+0x268f5c]  ClassPathZipEntry::open_entry(JavaThread*, char const*, int*, bool)+0xb4
V  [libjvm.dylib+0x269090]  ClassPathZipEntry::open_stream(JavaThread*, char const*)+0x20
V  [libjvm.dylib+0x26c5d4]  ClassLoader::load_class(Symbol*, bool, JavaThread*)+0x144
V  [libjvm.dylib+0x9abffc]  SystemDictionary::load_instance_class_impl(Symbol*, Handle, JavaThread*)+0x308
V  [libjvm.dylib+0x9aa7a0]  SystemDictionary::load_instance_class(unsigned int, Symbol*, Handle, JavaThread*)+0x30
V  [libjvm.dylib+0x9a9e18]  SystemDictionary::resolve_instance_class_or_null(Symbol*, Handle, Handle, JavaThread*)+0x480
V  [libjvm.dylib+0x9a93b0]  SystemDictionary::resolve_or_fail(Symbol*, Handle, Handle, bool, JavaThread*)+0xa0
V  [libjvm.dylib+0x2cb908]  ConstantPool::klass_at_impl(constantPoolHandle const&, int, JavaThread*)+0x1e4
V  [libjvm.dylib+0x4859b0]  InterpreterRuntime::_new(JavaThread*, ConstantPool*, int)+0xa8
j  com.intellij.rt.debugger.agent.CaptureStorage.insertExit(Ljava/lang/Object;)V+7
j  java.util.concurrent.FutureTask.run()V+14 java.base@17.0.15
j  java.util.concurrent.ThreadPoolExecutor.runWorker(Ljava/util/concurrent/ThreadPoolExecutor$Worker;)V+92 java.base@17.0.15
j  java.util.concurrent.ThreadPoolExecutor$Worker.run()V+5 java.base@17.0.15
j  java.lang.Thread.run()V+11 java.base@17.0.15
v  ~StubRoutines::call_stub
V  [libjvm.dylib+0x490448]  JavaCalls::call_helper(JavaValue*, methodHandle const&, JavaCallArguments*, JavaThread*)+0x38c
V  [libjvm.dylib+0x48f414]  JavaCalls::call_virtual(JavaValue*, Klass*, Symbol*, Symbol*, JavaCallArguments*, JavaThread*)+0x11c
V  [libjvm.dylib+0x48f4e0]  JavaCalls::call_virtual(JavaValue*, Handle, Klass*, Symbol*, Symbol*, JavaThread*)+0x64
V  [libjvm.dylib+0x549fd8]  thread_entry(JavaThread*, JavaThread*)+0xc4
V  [libjvm.dylib+0x9eae10]  JavaThread::thread_main_inner()+0x150
V  [libjvm.dylib+0x9e945c]  Thread::call_run()+0xcc
V  [libjvm.dylib+0x7fd0d8]  thread_native_entry(Thread*)+0x158
C  [libsystem_pthread.dylib+0x6c0c]  _pthread_start+0x88

Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
j  com.intellij.rt.debugger.agent.CaptureStorage.insertExit(Ljava/lang/Object;)V+7
j  java.util.concurrent.FutureTask.run()V+14 java.base@17.0.15
j  java.util.concurrent.ThreadPoolExecutor.runWorker(Ljava/util/concurrent/ThreadPoolExecutor$Worker;)V+92 java.base@17.0.15
j  java.util.concurrent.ThreadPoolExecutor$Worker.run()V+5 java.base@17.0.15
j  java.lang.Thread.run()V+11 java.base@17.0.15
v  ~StubRoutines::call_stub

siginfo: si_signo: 10 (SIGBUS), si_code: 1 (BUS_ADRALN), si_addr: 0x00000001008c38d7

Registers:
 x0=0x000060000b637610  x1=0x000000000000001e  x2=0x000000004027aefe  x3=0x0000000000000004
 x4=0x0000000000000005  x5=0x00000000b0e0f7fb  x6=0x000060000b637610  x7=0x0000000000000000
 x8=0x00000001009d38bb  x9=0x00000000001178bb x10=0x0000000000110000 x11=0x0000000000003610
x12=0x0000000000000050 x13=0x0000000000000001 x14=0x00000000ffffffe1 x15=0x00000000000007fb
x16=0x00000000b0e0f7fb x17=0x00000000000000ad x18=0x0000000000000000 x19=0x000060000b637610
x20=0x0000000000000000 x21=0x0000600002c4d8c0 x22=0x00000001008c38bb x23=0x00000000ba5adc06
x24=0x000000000000002f x25=0x0000000000000035 x26=0x000060000b637638 x27=0x000000010c022800
x28=0x0000000101c04c90  fp=0x000000030373e050  lr=0x00000001009ebbf8  sp=0x000000030373dfd0
pc=0x00000001009ebc2c cpsr=0x0000000060001000

Register to memory mapping:

 x0=0x000060000b637610 points into unknown readable memory: 0x0000000000000000 | 00 00 00 00 00 00 00 00
 x1=0x000000000000001e is an unknown value
 x2=0x000000004027aefe is an unknown value
 x3=0x0000000000000004 is an unknown value
 x4=0x0000000000000005 is an unknown value
 x5=0x00000000b0e0f7fb is an unknown value
 x6=0x000060000b637610 points into unknown readable memory: 0x0000000000000000 | 00 00 00 00 00 00 00 00
 x7=0x0 is NULL
 x8=0x00000001009d38bb is an unknown value
 x9=0x00000000001178bb is an unknown value
x10=0x0000000000110000 is an unknown value
x11=0x0000000000003610 is an unknown value
x12=0x0000000000000050 is an unknown value
x13=0x0000000000000001 is an unknown value
x14=0x00000000ffffffe1 is an unknown value
x15=0x00000000000007fb is an unknown value
x16=0x00000000b0e0f7fb is an unknown value
x17=0x00000000000000ad is an unknown value
x18=0x0 is NULL
x19=0x000060000b637610 points into unknown readable memory: 0x0000000000000000 | 00 00 00 00 00 00 00 00
x20=0x0 is NULL
x21=0x0000600002c4d8c0 points into unknown readable memory: 0x000060000b34f660 | 60 f6 34 0b 00 60 00 00
x22=0x00000001008c38bb points into unknown readable memory: 50 4b 01 02 00
x23=0x00000000ba5adc06 is an unknown value
x24=0x000000000000002f is an unknown value
x25=0x0000000000000035 is an unknown value
x26=0x000060000b637638 points into unknown readable memory: 0x0000000000000000 | 00 00 00 00 00 00 00 00
x27=0x000000010c022800 points into unknown readable memory: 0xffffffff5bbd78a2 | a2 78 bd 5b ff ff ff ff
x28=0x0000000101c04c90 points into unknown readable memory: 0x65746e692f6d6f63 | 63 6f 6d 2f 69 6e 74 65


Top of Stack: (sp=0x000000030373dfd0)
0x000000030373dfd0:   0000000000000000 0000000000000000
0x000000030373dfe0:   0000000000000000 0000000000000000
0x000000030373dff0:   0000000000000000 0000000000000000
0x000000030373e000:   0000000101c04c90 000000010c022800
0x000000030373e010:   00000000000000da 0000000000000035
0x000000030373e020:   000000000000002f 00000000ba5adc06
0x000000030373e030:   000060000b637520 0000000000000000
0x000000030373e040:   0000000101c04cc0 0000600002c4d8c0
0x000000030373e050:   000000030373e0b0 00000001009ebb00
0x000000030373e060:   0000000101c04c90 000000000000002f
0x000000030373e070:   0000000000000001 0000000101c04cc0
0x000000030373e080:   0000000131bb6738 0000000101c04cc0
0x000000030373e090:   0000600002c4d8c0 0000000101c04cc0
0x000000030373e0a0:   000000030373e1dc 000000030373e0f4
0x000000030373e0b0:   000000030373e0e0 00000001009ec540
0x000000030373e0c0:   000000030373e1dc 000060000469a130
0x000000030373e0d0:   0000000000000000 0000000131bb6400
0x000000030373e0e0:   000000030373e1c0 0000000101fe0f5c
0x000000030373e0f0:   000000010286c177 0000000000000100
0x000000030373e100:   000000030373e120 00000001022c19d8
0x000000030373e110:   000000010286c177 000000030373e1a0
0x000000030373e120:   000000030373e170 0000000101fe5e4c
0x000000030373e130:   0000000000000000 0000000000000000
0x000000030373e140:   0000000000000001 000000013819a890
0x000000030373e150:   0000000131bb6400 00000000000003d8
0x000000030373e160:   00000001028810e5 000000030373e268
0x000000030373e170:   000000030373e190 0c5ca61589390000
0x000000030373e180:   0000000000000001 0000000101c04cc0
0x000000030373e190:   000060000469a130 000000013819a890
0x000000030373e1a0:   0000000131bb6400 00000000000003d8
0x000000030373e1b0:   0000000101c04c80 000060000469a130
0x000000030373e1c0:   000000030373e1f0 0000000101fe1090 

Instructions: (pc=0x00000001009ebc2c)
0x00000001009ebb2c:   4c 15 40 38 7f 01 0c 6b 60 ff ff 54 de ff ff 17
0x00000001009ebb3c:   16 00 80 d2 76 01 00 b5 55 01 00 34 3f 07 00 f1
0x00000001009ebb4c:   eb f7 ff 54 28 07 00 51 88 4a 68 38 1f bd 00 71
0x00000001009ebb5c:   61 f7 ff 54 16 00 80 d2 02 00 00 14 7f 4e 00 f9
0x00000001009ebb6c:   60 2a 40 f9 a7 04 00 94 e0 03 16 aa fd 7b 45 a9
0x00000001009ebb7c:   f4 4f 44 a9 f6 57 43 a9 f8 5f 42 a9 fa 67 41 a9
0x00000001009ebb8c:   fc 6f c6 a8 c0 03 5f d6 3f 00 03 6b e1 00 00 54
0x00000001009ebb9c:   21 04 00 71 eb 00 00 54 08 14 40 38 49 14 40 38
0x00000001009ebbac:   1f 01 09 6b 60 ff ff 54 00 00 80 52 c0 03 5f d6
0x00000001009ebbbc:   20 00 80 52 c0 03 5f d6 ff 43 02 d1 fc 6f 03 a9
0x00000001009ebbcc:   fa 67 04 a9 f8 5f 05 a9 f6 57 06 a9 f4 4f 07 a9
0x00000001009ebbdc:   fd 7b 08 a9 fd 03 02 91 f4 03 02 aa f6 03 01 aa
0x00000001009ebbec:   f5 03 00 aa 00 09 80 52 ad 04 00 94 f3 03 00 aa
0x00000001009ebbfc:   40 13 00 b4 7f 02 00 f9 fa 03 13 aa 5f 8f 02 f8
0x00000001009ebc0c:   7f 1a 00 f9 a8 c2 40 39 88 02 00 34 a8 0e 40 f9
0x00000001009ebc1c:   c9 06 40 f9 aa 16 40 f9 08 01 09 8b 16 01 0a cb
0x00000001009ebc2c:   d8 3a 40 79 d7 7a 40 39 db 7e 40 39 c8 42 40 79
0x00000001009ebc3c:   e8 17 00 f9 c8 0e 40 b9 68 06 00 f9 c8 1a 40 b9
0x00000001009ebc4c:   e8 0f 00 f9 68 0a 00 f9 c8 16 40 79 88 04 00 34
0x00000001009ebc5c:   c8 16 40 b9 23 00 00 14 d7 06 40 f9 34 0d 00 34
0x00000001009ebc6c:   a8 1e 40 f9 88 02 00 b4 a9 22 40 f9 3f 01 17 eb
0x00000001009ebc7c:   2c 02 00 54 4a fa 83 52 2a 01 0a 8b 5f 01 17 eb
0x00000001009ebc8c:   ab 01 00 54 2a 09 40 91 08 01 17 8b 16 01 09 cb
0x00000001009ebc9c:   c8 3a 40 79 c9 3e 40 79 cb 42 40 79 e8 02 08 8b
0x00000001009ebcac:   08 01 09 8b 08 01 0b 8b 08 b9 00 91 1f 01 0a eb
0x00000001009ebcbc:   2d 0b 00 54 e0 03 15 aa e1 03 17 aa 02 00 84 52
0x00000001009ebccc:   b8 03 00 94 f6 03 00 aa 80 0a 00 b4 a0 1e 40 f9
0x00000001009ebcdc:   64 04 00 94 b6 de 03 a9 d2 ff ff 17 08 00 80 d2
0x00000001009ebcec:   fc 03 08 aa 68 0e 00 f9 c8 12 40 b9 68 22 00 b9
0x00000001009ebcfc:   c9 a2 42 b8 a8 5e 40 f9 e9 0b 00 f9 08 01 09 8b
0x00000001009ebd0c:   e8 03 08 cb 68 1e 00 f9 c8 12 40 79 68 42 00 b9
0x00000001009ebd1c:   00 07 00 91 62 04 00 94 f9 03 00 aa 60 02 00 f9 


Stack slot to memory mapping:
stack at sp + 0 slots: 0x0 is NULL
stack at sp + 1 slots: 0x0 is NULL
stack at sp + 2 slots: 0x0 is NULL
stack at sp + 3 slots: 0x0 is NULL
stack at sp + 4 slots: 0x0 is NULL
stack at sp + 5 slots: 0x0 is NULL
stack at sp + 6 slots: 0x0000000101c04c90 points into unknown readable memory: 0x65746e692f6d6f63 | 63 6f 6d 2f 69 6e 74 65
stack at sp + 7 slots: 0x000000010c022800 points into unknown readable memory: 0xffffffff5bbd78a2 | a2 78 bd 5b ff ff ff ff


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000600005675fe0, length=16, elements={
0x000000010c019c00, 0x000000010b2be600, 0x000000010b2bec00, 0x0000000142877600,
0x00000001318a4a00, 0x00000001318a5000, 0x00000001318a8600, 0x000000010b2c3c00,
0x00000001420c3000, 0x000000010b8dc400, 0x000000010b8dca00, 0x000000010b2c4200,
0x00000001318a8000, 0x0000000142871200, 0x000000010bab1c00, 0x0000000131bb6400
}

Java Threads: ( => current thread )
  0x000000010c019c00 JavaThread "main" [_thread_blocked, id=5635, stack(0x000000016f698000,0x000000016f89b000)]
  0x000000010b2be600 JavaThread "Reference Handler" daemon [_thread_blocked, id=31235, stack(0x0000000300628000,0x000000030082b000)]
  0x000000010b2bec00 JavaThread "Finalizer" daemon [_thread_blocked, id=23043, stack(0x0000000300834000,0x0000000300a37000)]
  0x0000000142877600 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=28931, stack(0x0000000300a40000,0x0000000300c43000)]
  0x00000001318a4a00 JavaThread "Service Thread" daemon [_thread_blocked, id=28419, stack(0x0000000300c4c000,0x0000000300e4f000)]
  0x00000001318a5000 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=24835, stack(0x0000000300e58000,0x000000030105b000)]
  0x00000001318a8600 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=25091, stack(0x0000000301064000,0x0000000301267000)]
  0x000000010b2c3c00 JavaThread "Sweeper thread" daemon [_thread_blocked, id=25347, stack(0x0000000301270000,0x0000000301473000)]
  0x00000001420c3000 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=27651, stack(0x000000030147c000,0x000000030167f000)]
  0x000000010b8dc400 JavaThread "JDWP Transport Listener: dt_socket" daemon [_thread_blocked, id=27139, stack(0x0000000301688000,0x000000030188b000)]
  0x000000010b8dca00 JavaThread "JDWP Event Helper Thread" daemon [_thread_blocked, id=25859, stack(0x0000000301894000,0x0000000301a97000)]
  0x000000010b2c4200 JavaThread "JDWP Command Reader" daemon [_thread_in_native, id=26371, stack(0x0000000301aa0000,0x0000000301ca3000)]
  0x00000001318a8000 JavaThread "IntelliJ Suspend Helper" daemon [_thread_blocked, id=32771, stack(0x0000000301cac000,0x0000000301eaf000)]
  0x0000000142871200 JavaThread "Notification Thread" daemon [_thread_blocked, id=33283, stack(0x0000000301eb8000,0x00000003020bb000)]
  0x000000010bab1c00 JavaThread "RMI TCP Accept-0" daemon [_thread_in_native, id=33539, stack(0x00000003020c4000,0x00000003022c7000)]
=>0x0000000131bb6400 JavaThread "spring.cloud.inetutils" daemon [_thread_in_native, id=39683, stack(0x000000030353c000,0x000000030373f000)]

Other Threads:
  0x000000013178db10 VMThread "VM Thread" [stack: 0x000000030041c000,0x000000030061f000] [id=17923]
  0x000000013179a450 WatcherThread [stack: 0x00000003022d0000,0x00000003024d3000] [id=43011]
  0x00000001116243e0 GCTaskThread "GC Thread#0" [stack: 0x000000016f8a4000,0x000000016faa7000] [id=14339]
  0x00000001019806f0 GCTaskThread "GC Thread#1" [stack: 0x00000003024dc000,0x00000003026df000] [id=42499]
  0x0000000101980b70 GCTaskThread "GC Thread#2" [stack: 0x00000003026e8000,0x00000003028eb000] [id=42243]
  0x00000001019813e0 GCTaskThread "GC Thread#3" [stack: 0x00000003028f4000,0x0000000302af7000] [id=34307]
  0x0000000101981c50 GCTaskThread "GC Thread#4" [stack: 0x0000000302b00000,0x0000000302d03000] [id=41731]
  0x00000001019824c0 GCTaskThread "GC Thread#5" [stack: 0x0000000302d0c000,0x0000000302f0f000] [id=41219]
  0x0000000101982f70 GCTaskThread "GC Thread#6" [stack: 0x0000000302f18000,0x000000030311b000] [id=40707]
  0x0000000132087be0 GCTaskThread "GC Thread#7" [stack: 0x0000000303124000,0x0000000303327000] [id=34819]
  0x000000013166b790 GCTaskThread "GC Thread#8" [stack: 0x0000000303330000,0x0000000303533000] [id=40195]
  0x0000000101967be0 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000016fab0000,0x000000016fcb3000] [id=12547]
  0x0000000101968470 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000016fcbc000,0x000000016febf000] [id=13571]
  0x0000000101a31410 ConcurrentGCThread "G1 Refine#0" [stack: 0x0000000300004000,0x0000000300207000] [id=21507]
  0x000000014163e040 ConcurrentGCThread "G1 Service" [stack: 0x0000000300210000,0x0000000300413000] [id=20995]

Threads with active compile tasks:

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000700000000, size: 4096 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000008000000000-0x0000008000bc0000-0x0000008000bc0000), size 12320768, SharedBaseAddress: 0x0000008000000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000008001000000-0x0000008041000000, reserved size: 1073741824
Narrow klass base: 0x0000008000000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 10 total, 10 available
 Memory: 16384M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 2M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 256M
 Heap Max Capacity: 4G
 Pre-touch: Disabled
 Parallel Workers: 9
 Concurrent Workers: 2
 Concurrent Refinement Workers: 9
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 266240K, used 33166K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 14 young (28672K), 2 survivors (4096K)
 Metaspace       used 13308K, committed 13504K, reserved 1114112K
  class space    used 1548K, committed 1664K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000700000000, 0x0000000700200000, 0x0000000700200000|100%| O|  |TAMS 0x0000000700000000, 0x0000000700000000| Untracked 
|   1|0x0000000700200000, 0x0000000700400000, 0x0000000700400000|100%| O|  |TAMS 0x0000000700200000, 0x0000000700200000| Untracked 
|   2|0x0000000700400000, 0x000000070047ba00, 0x0000000700600000| 24%| O|  |TAMS 0x0000000700400000, 0x0000000700400000| Untracked 
|   3|0x0000000700600000, 0x0000000700600000, 0x0000000700800000|  0%| F|  |TAMS 0x0000000700600000, 0x0000000700600000| Untracked 
|   4|0x0000000700800000, 0x0000000700800000, 0x0000000700a00000|  0%| F|  |TAMS 0x0000000700800000, 0x0000000700800000| Untracked 
|   5|0x0000000700a00000, 0x0000000700a00000, 0x0000000700c00000|  0%| F|  |TAMS 0x0000000700a00000, 0x0000000700a00000| Untracked 
|   6|0x0000000700c00000, 0x0000000700c00000, 0x0000000700e00000|  0%| F|  |TAMS 0x0000000700c00000, 0x0000000700c00000| Untracked 
|   7|0x0000000700e00000, 0x0000000700e00000, 0x0000000701000000|  0%| F|  |TAMS 0x0000000700e00000, 0x0000000700e00000| Untracked 
|   8|0x0000000701000000, 0x0000000701000000, 0x0000000701200000|  0%| F|  |TAMS 0x0000000701000000, 0x0000000701000000| Untracked 
|   9|0x0000000701200000, 0x0000000701200000, 0x0000000701400000|  0%| F|  |TAMS 0x0000000701200000, 0x0000000701200000| Untracked 
|  10|0x0000000701400000, 0x0000000701400000, 0x0000000701600000|  0%| F|  |TAMS 0x0000000701400000, 0x0000000701400000| Untracked 
|  11|0x0000000701600000, 0x0000000701600000, 0x0000000701800000|  0%| F|  |TAMS 0x0000000701600000, 0x0000000701600000| Untracked 
|  12|0x0000000701800000, 0x0000000701800000, 0x0000000701a00000|  0%| F|  |TAMS 0x0000000701800000, 0x0000000701800000| Untracked 
|  13|0x0000000701a00000, 0x0000000701a00000, 0x0000000701c00000|  0%| F|  |TAMS 0x0000000701a00000, 0x0000000701a00000| Untracked 
|  14|0x0000000701c00000, 0x0000000701c00000, 0x0000000701e00000|  0%| F|  |TAMS 0x0000000701c00000, 0x0000000701c00000| Untracked 
|  15|0x0000000701e00000, 0x0000000701e00000, 0x0000000702000000|  0%| F|  |TAMS 0x0000000701e00000, 0x0000000701e00000| Untracked 
|  16|0x0000000702000000, 0x0000000702000000, 0x0000000702200000|  0%| F|  |TAMS 0x0000000702000000, 0x0000000702000000| Untracked 
|  17|0x0000000702200000, 0x0000000702200000, 0x0000000702400000|  0%| F|  |TAMS 0x0000000702200000, 0x0000000702200000| Untracked 
|  18|0x0000000702400000, 0x0000000702400000, 0x0000000702600000|  0%| F|  |TAMS 0x0000000702400000, 0x0000000702400000| Untracked 
|  19|0x0000000702600000, 0x0000000702600000, 0x0000000702800000|  0%| F|  |TAMS 0x0000000702600000, 0x0000000702600000| Untracked 
|  20|0x0000000702800000, 0x0000000702800000, 0x0000000702a00000|  0%| F|  |TAMS 0x0000000702800000, 0x0000000702800000| Untracked 
|  21|0x0000000702a00000, 0x0000000702a00000, 0x0000000702c00000|  0%| F|  |TAMS 0x0000000702a00000, 0x0000000702a00000| Untracked 
|  22|0x0000000702c00000, 0x0000000702c00000, 0x0000000702e00000|  0%| F|  |TAMS 0x0000000702c00000, 0x0000000702c00000| Untracked 
|  23|0x0000000702e00000, 0x0000000702e00000, 0x0000000703000000|  0%| F|  |TAMS 0x0000000702e00000, 0x0000000702e00000| Untracked 
|  24|0x0000000703000000, 0x0000000703000000, 0x0000000703200000|  0%| F|  |TAMS 0x0000000703000000, 0x0000000703000000| Untracked 
|  25|0x0000000703200000, 0x0000000703200000, 0x0000000703400000|  0%| F|  |TAMS 0x0000000703200000, 0x0000000703200000| Untracked 
|  26|0x0000000703400000, 0x0000000703400000, 0x0000000703600000|  0%| F|  |TAMS 0x0000000703400000, 0x0000000703400000| Untracked 
|  27|0x0000000703600000, 0x0000000703600000, 0x0000000703800000|  0%| F|  |TAMS 0x0000000703600000, 0x0000000703600000| Untracked 
|  28|0x0000000703800000, 0x0000000703800000, 0x0000000703a00000|  0%| F|  |TAMS 0x0000000703800000, 0x0000000703800000| Untracked 
|  29|0x0000000703a00000, 0x0000000703a00000, 0x0000000703c00000|  0%| F|  |TAMS 0x0000000703a00000, 0x0000000703a00000| Untracked 
|  30|0x0000000703c00000, 0x0000000703c00000, 0x0000000703e00000|  0%| F|  |TAMS 0x0000000703c00000, 0x0000000703c00000| Untracked 
|  31|0x0000000703e00000, 0x0000000703e00000, 0x0000000704000000|  0%| F|  |TAMS 0x0000000703e00000, 0x0000000703e00000| Untracked 
|  32|0x0000000704000000, 0x0000000704000000, 0x0000000704200000|  0%| F|  |TAMS 0x0000000704000000, 0x0000000704000000| Untracked 
|  33|0x0000000704200000, 0x0000000704200000, 0x0000000704400000|  0%| F|  |TAMS 0x0000000704200000, 0x0000000704200000| Untracked 
|  34|0x0000000704400000, 0x0000000704400000, 0x0000000704600000|  0%| F|  |TAMS 0x0000000704400000, 0x0000000704400000| Untracked 
|  35|0x0000000704600000, 0x0000000704600000, 0x0000000704800000|  0%| F|  |TAMS 0x0000000704600000, 0x0000000704600000| Untracked 
|  36|0x0000000704800000, 0x0000000704800000, 0x0000000704a00000|  0%| F|  |TAMS 0x0000000704800000, 0x0000000704800000| Untracked 
|  37|0x0000000704a00000, 0x0000000704a00000, 0x0000000704c00000|  0%| F|  |TAMS 0x0000000704a00000, 0x0000000704a00000| Untracked 
|  38|0x0000000704c00000, 0x0000000704c00000, 0x0000000704e00000|  0%| F|  |TAMS 0x0000000704c00000, 0x0000000704c00000| Untracked 
|  39|0x0000000704e00000, 0x0000000704e00000, 0x0000000705000000|  0%| F|  |TAMS 0x0000000704e00000, 0x0000000704e00000| Untracked 
|  40|0x0000000705000000, 0x0000000705000000, 0x0000000705200000|  0%| F|  |TAMS 0x0000000705000000, 0x0000000705000000| Untracked 
|  41|0x0000000705200000, 0x0000000705200000, 0x0000000705400000|  0%| F|  |TAMS 0x0000000705200000, 0x0000000705200000| Untracked 
|  42|0x0000000705400000, 0x0000000705400000, 0x0000000705600000|  0%| F|  |TAMS 0x0000000705400000, 0x0000000705400000| Untracked 
|  43|0x0000000705600000, 0x0000000705600000, 0x0000000705800000|  0%| F|  |TAMS 0x0000000705600000, 0x0000000705600000| Untracked 
|  44|0x0000000705800000, 0x0000000705800000, 0x0000000705a00000|  0%| F|  |TAMS 0x0000000705800000, 0x0000000705800000| Untracked 
|  45|0x0000000705a00000, 0x0000000705a00000, 0x0000000705c00000|  0%| F|  |TAMS 0x0000000705a00000, 0x0000000705a00000| Untracked 
|  46|0x0000000705c00000, 0x0000000705c00000, 0x0000000705e00000|  0%| F|  |TAMS 0x0000000705c00000, 0x0000000705c00000| Untracked 
|  47|0x0000000705e00000, 0x0000000705e00000, 0x0000000706000000|  0%| F|  |TAMS 0x0000000705e00000, 0x0000000705e00000| Untracked 
|  48|0x0000000706000000, 0x0000000706000000, 0x0000000706200000|  0%| F|  |TAMS 0x0000000706000000, 0x0000000706000000| Untracked 
|  49|0x0000000706200000, 0x0000000706200000, 0x0000000706400000|  0%| F|  |TAMS 0x0000000706200000, 0x0000000706200000| Untracked 
|  50|0x0000000706400000, 0x0000000706400000, 0x0000000706600000|  0%| F|  |TAMS 0x0000000706400000, 0x0000000706400000| Untracked 
|  51|0x0000000706600000, 0x0000000706600000, 0x0000000706800000|  0%| F|  |TAMS 0x0000000706600000, 0x0000000706600000| Untracked 
|  52|0x0000000706800000, 0x0000000706800000, 0x0000000706a00000|  0%| F|  |TAMS 0x0000000706800000, 0x0000000706800000| Untracked 
|  53|0x0000000706a00000, 0x0000000706a00000, 0x0000000706c00000|  0%| F|  |TAMS 0x0000000706a00000, 0x0000000706a00000| Untracked 
|  54|0x0000000706c00000, 0x0000000706c00000, 0x0000000706e00000|  0%| F|  |TAMS 0x0000000706c00000, 0x0000000706c00000| Untracked 
|  55|0x0000000706e00000, 0x0000000706e00000, 0x0000000707000000|  0%| F|  |TAMS 0x0000000706e00000, 0x0000000706e00000| Untracked 
|  56|0x0000000707000000, 0x0000000707000000, 0x0000000707200000|  0%| F|  |TAMS 0x0000000707000000, 0x0000000707000000| Untracked 
|  57|0x0000000707200000, 0x0000000707200000, 0x0000000707400000|  0%| F|  |TAMS 0x0000000707200000, 0x0000000707200000| Untracked 
|  58|0x0000000707400000, 0x0000000707400000, 0x0000000707600000|  0%| F|  |TAMS 0x0000000707400000, 0x0000000707400000| Untracked 
|  59|0x0000000707600000, 0x0000000707600000, 0x0000000707800000|  0%| F|  |TAMS 0x0000000707600000, 0x0000000707600000| Untracked 
|  60|0x0000000707800000, 0x0000000707800000, 0x0000000707a00000|  0%| F|  |TAMS 0x0000000707800000, 0x0000000707800000| Untracked 
|  61|0x0000000707a00000, 0x0000000707a00000, 0x0000000707c00000|  0%| F|  |TAMS 0x0000000707a00000, 0x0000000707a00000| Untracked 
|  62|0x0000000707c00000, 0x0000000707c00000, 0x0000000707e00000|  0%| F|  |TAMS 0x0000000707c00000, 0x0000000707c00000| Untracked 
|  63|0x0000000707e00000, 0x0000000707e00000, 0x0000000708000000|  0%| F|  |TAMS 0x0000000707e00000, 0x0000000707e00000| Untracked 
|  64|0x0000000708000000, 0x0000000708000000, 0x0000000708200000|  0%| F|  |TAMS 0x0000000708000000, 0x0000000708000000| Untracked 
|  65|0x0000000708200000, 0x0000000708200000, 0x0000000708400000|  0%| F|  |TAMS 0x0000000708200000, 0x0000000708200000| Untracked 
|  66|0x0000000708400000, 0x0000000708400000, 0x0000000708600000|  0%| F|  |TAMS 0x0000000708400000, 0x0000000708400000| Untracked 
|  67|0x0000000708600000, 0x0000000708600000, 0x0000000708800000|  0%| F|  |TAMS 0x0000000708600000, 0x0000000708600000| Untracked 
|  68|0x0000000708800000, 0x0000000708800000, 0x0000000708a00000|  0%| F|  |TAMS 0x0000000708800000, 0x0000000708800000| Untracked 
|  69|0x0000000708a00000, 0x0000000708a00000, 0x0000000708c00000|  0%| F|  |TAMS 0x0000000708a00000, 0x0000000708a00000| Untracked 
|  70|0x0000000708c00000, 0x0000000708c00000, 0x0000000708e00000|  0%| F|  |TAMS 0x0000000708c00000, 0x0000000708c00000| Untracked 
|  71|0x0000000708e00000, 0x0000000708e00000, 0x0000000709000000|  0%| F|  |TAMS 0x0000000708e00000, 0x0000000708e00000| Untracked 
|  72|0x0000000709000000, 0x0000000709000000, 0x0000000709200000|  0%| F|  |TAMS 0x0000000709000000, 0x0000000709000000| Untracked 
|  73|0x0000000709200000, 0x0000000709200000, 0x0000000709400000|  0%| F|  |TAMS 0x0000000709200000, 0x0000000709200000| Untracked 
|  74|0x0000000709400000, 0x0000000709400000, 0x0000000709600000|  0%| F|  |TAMS 0x0000000709400000, 0x0000000709400000| Untracked 
|  75|0x0000000709600000, 0x0000000709600000, 0x0000000709800000|  0%| F|  |TAMS 0x0000000709600000, 0x0000000709600000| Untracked 
|  76|0x0000000709800000, 0x0000000709800000, 0x0000000709a00000|  0%| F|  |TAMS 0x0000000709800000, 0x0000000709800000| Untracked 
|  77|0x0000000709a00000, 0x0000000709a00000, 0x0000000709c00000|  0%| F|  |TAMS 0x0000000709a00000, 0x0000000709a00000| Untracked 
|  78|0x0000000709c00000, 0x0000000709c00000, 0x0000000709e00000|  0%| F|  |TAMS 0x0000000709c00000, 0x0000000709c00000| Untracked 
|  79|0x0000000709e00000, 0x0000000709e00000, 0x000000070a000000|  0%| F|  |TAMS 0x0000000709e00000, 0x0000000709e00000| Untracked 
|  80|0x000000070a000000, 0x000000070a000000, 0x000000070a200000|  0%| F|  |TAMS 0x000000070a000000, 0x000000070a000000| Untracked 
|  81|0x000000070a200000, 0x000000070a200000, 0x000000070a400000|  0%| F|  |TAMS 0x000000070a200000, 0x000000070a200000| Untracked 
|  82|0x000000070a400000, 0x000000070a400000, 0x000000070a600000|  0%| F|  |TAMS 0x000000070a400000, 0x000000070a400000| Untracked 
|  83|0x000000070a600000, 0x000000070a600000, 0x000000070a800000|  0%| F|  |TAMS 0x000000070a600000, 0x000000070a600000| Untracked 
|  84|0x000000070a800000, 0x000000070a800000, 0x000000070aa00000|  0%| F|  |TAMS 0x000000070a800000, 0x000000070a800000| Untracked 
|  85|0x000000070aa00000, 0x000000070aa00000, 0x000000070ac00000|  0%| F|  |TAMS 0x000000070aa00000, 0x000000070aa00000| Untracked 
|  86|0x000000070ac00000, 0x000000070ac00000, 0x000000070ae00000|  0%| F|  |TAMS 0x000000070ac00000, 0x000000070ac00000| Untracked 
|  87|0x000000070ae00000, 0x000000070ae00000, 0x000000070b000000|  0%| F|  |TAMS 0x000000070ae00000, 0x000000070ae00000| Untracked 
|  88|0x000000070b000000, 0x000000070b000000, 0x000000070b200000|  0%| F|  |TAMS 0x000000070b000000, 0x000000070b000000| Untracked 
|  89|0x000000070b200000, 0x000000070b200000, 0x000000070b400000|  0%| F|  |TAMS 0x000000070b200000, 0x000000070b200000| Untracked 
|  90|0x000000070b400000, 0x000000070b400000, 0x000000070b600000|  0%| F|  |TAMS 0x000000070b400000, 0x000000070b400000| Untracked 
|  91|0x000000070b600000, 0x000000070b600000, 0x000000070b800000|  0%| F|  |TAMS 0x000000070b600000, 0x000000070b600000| Untracked 
|  92|0x000000070b800000, 0x000000070b800000, 0x000000070ba00000|  0%| F|  |TAMS 0x000000070b800000, 0x000000070b800000| Untracked 
|  93|0x000000070ba00000, 0x000000070ba00000, 0x000000070bc00000|  0%| F|  |TAMS 0x000000070ba00000, 0x000000070ba00000| Untracked 
|  94|0x000000070bc00000, 0x000000070bc00000, 0x000000070be00000|  0%| F|  |TAMS 0x000000070bc00000, 0x000000070bc00000| Untracked 
|  95|0x000000070be00000, 0x000000070be00000, 0x000000070c000000|  0%| F|  |TAMS 0x000000070be00000, 0x000000070be00000| Untracked 
|  96|0x000000070c000000, 0x000000070c000000, 0x000000070c200000|  0%| F|  |TAMS 0x000000070c000000, 0x000000070c000000| Untracked 
|  97|0x000000070c200000, 0x000000070c200000, 0x000000070c400000|  0%| F|  |TAMS 0x000000070c200000, 0x000000070c200000| Untracked 
|  98|0x000000070c400000, 0x000000070c400000, 0x000000070c600000|  0%| F|  |TAMS 0x000000070c400000, 0x000000070c400000| Untracked 
|  99|0x000000070c600000, 0x000000070c600000, 0x000000070c800000|  0%| F|  |TAMS 0x000000070c600000, 0x000000070c600000| Untracked 
| 100|0x000000070c800000, 0x000000070c800000, 0x000000070ca00000|  0%| F|  |TAMS 0x000000070c800000, 0x000000070c800000| Untracked 
| 101|0x000000070ca00000, 0x000000070ca00000, 0x000000070cc00000|  0%| F|  |TAMS 0x000000070ca00000, 0x000000070ca00000| Untracked 
| 102|0x000000070cc00000, 0x000000070cc00000, 0x000000070ce00000|  0%| F|  |TAMS 0x000000070cc00000, 0x000000070cc00000| Untracked 
| 103|0x000000070ce00000, 0x000000070ce00000, 0x000000070d000000|  0%| F|  |TAMS 0x000000070ce00000, 0x000000070ce00000| Untracked 
| 104|0x000000070d000000, 0x000000070d000000, 0x000000070d200000|  0%| F|  |TAMS 0x000000070d000000, 0x000000070d000000| Untracked 
| 105|0x000000070d200000, 0x000000070d200000, 0x000000070d400000|  0%| F|  |TAMS 0x000000070d200000, 0x000000070d200000| Untracked 
| 106|0x000000070d400000, 0x000000070d400000, 0x000000070d600000|  0%| F|  |TAMS 0x000000070d400000, 0x000000070d400000| Untracked 
| 107|0x000000070d600000, 0x000000070d600000, 0x000000070d800000|  0%| F|  |TAMS 0x000000070d600000, 0x000000070d600000| Untracked 
| 108|0x000000070d800000, 0x000000070d800000, 0x000000070da00000|  0%| F|  |TAMS 0x000000070d800000, 0x000000070d800000| Untracked 
| 109|0x000000070da00000, 0x000000070da00000, 0x000000070dc00000|  0%| F|  |TAMS 0x000000070da00000, 0x000000070da00000| Untracked 
| 110|0x000000070dc00000, 0x000000070dc00000, 0x000000070de00000|  0%| F|  |TAMS 0x000000070dc00000, 0x000000070dc00000| Untracked 
| 111|0x000000070de00000, 0x000000070de00000, 0x000000070e000000|  0%| F|  |TAMS 0x000000070de00000, 0x000000070de00000| Untracked 
| 112|0x000000070e000000, 0x000000070e000000, 0x000000070e200000|  0%| F|  |TAMS 0x000000070e000000, 0x000000070e000000| Untracked 
| 113|0x000000070e200000, 0x000000070e200000, 0x000000070e400000|  0%| F|  |TAMS 0x000000070e200000, 0x000000070e200000| Untracked 
| 114|0x000000070e400000, 0x000000070e516530, 0x000000070e600000| 54%| E|  |TAMS 0x000000070e400000, 0x000000070e400000| Complete 
| 115|0x000000070e600000, 0x000000070e800000, 0x000000070e800000|100%| S|CS|TAMS 0x000000070e600000, 0x000000070e600000| Complete 
| 116|0x000000070e800000, 0x000000070ea00000, 0x000000070ea00000|100%| S|CS|TAMS 0x000000070e800000, 0x000000070e800000| Complete 
| 117|0x000000070ea00000, 0x000000070ec00000, 0x000000070ec00000|100%| E|CS|TAMS 0x000000070ea00000, 0x000000070ea00000| Complete 
| 118|0x000000070ec00000, 0x000000070ee00000, 0x000000070ee00000|100%| E|CS|TAMS 0x000000070ec00000, 0x000000070ec00000| Complete 
| 119|0x000000070ee00000, 0x000000070f000000, 0x000000070f000000|100%| E|CS|TAMS 0x000000070ee00000, 0x000000070ee00000| Complete 
| 120|0x000000070f000000, 0x000000070f200000, 0x000000070f200000|100%| E|CS|TAMS 0x000000070f000000, 0x000000070f000000| Complete 
| 121|0x000000070f200000, 0x000000070f400000, 0x000000070f400000|100%| E|CS|TAMS 0x000000070f200000, 0x000000070f200000| Complete 
| 122|0x000000070f400000, 0x000000070f600000, 0x000000070f600000|100%| E|CS|TAMS 0x000000070f400000, 0x000000070f400000| Complete 
| 123|0x000000070f600000, 0x000000070f800000, 0x000000070f800000|100%| E|CS|TAMS 0x000000070f600000, 0x000000070f600000| Complete 
| 124|0x000000070f800000, 0x000000070fa00000, 0x000000070fa00000|100%| E|CS|TAMS 0x000000070f800000, 0x000000070f800000| Complete 
| 125|0x000000070fa00000, 0x000000070fc00000, 0x000000070fc00000|100%| E|CS|TAMS 0x000000070fa00000, 0x000000070fa00000| Complete 
| 126|0x000000070fc00000, 0x000000070fe00000, 0x000000070fe00000|100%| E|CS|TAMS 0x000000070fc00000, 0x000000070fc00000| Complete 
| 127|0x000000070fe00000, 0x0000000710000000, 0x0000000710000000|100%| E|CS|TAMS 0x000000070fe00000, 0x000000070fe00000| Complete 
|2046|0x00000007ffc00000, 0x00000007ffd74000, 0x00000007ffe00000| 72%|OA|  |TAMS 0x00000007ffc00000, 0x00000007ffc00000| Untracked 
|2047|0x00000007ffe00000, 0x00000007ffe74000, 0x0000000800000000| 22%|CA|  |TAMS 0x00000007ffe00000, 0x00000007ffe00000| Untracked 

Card table byte_map: [0x0000000110000000,0x0000000110800000] _byte_map_base: 0x000000010c800000

Marking Bits (Prev, Next): (CMBitMap*) 0x000000010b8a7e10, (CMBitMap*) 0x000000010b8a7e50
 Prev Bits: [0x0000000111700000, 0x0000000115700000)
 Next Bits: [0x0000000122000000, 0x0000000126000000)

Polling page: 0x0000000100884000

Metaspace:

Usage:
  Non-class:     11.48 MB used.
      Class:      1.51 MB used.
       Both:     13.00 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      11.56 MB ( 18%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       1.62 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      13.19 MB (  1%) committed. 

Chunk freelists:
   Non-Class:  3.75 MB
       Class:  14.35 MB
        Both:  18.10 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 136.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 211.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 318.
num_chunk_merges: 0.
num_chunk_splits: 202.
num_chunks_enlarged: 149.
num_inconsistent_stats: 0.

CodeCache: size=49152Kb used=3920Kb max_used=3920Kb free=45231Kb
 bounds [0x000000010c800000, 0x000000010cbe0000, 0x000000010f800000]
 total_blobs=1966 nmethods=1519 adapters=379
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 0.848 Thread 0x00000001318a8600 1534       1       java.util.concurrent.CopyOnWriteArrayList::size (6 bytes)
Event: 0.849 Thread 0x00000001318a8600 nmethod 1534 0x000000010cbd0310 code [0x000000010cbd0480, 0x000000010cbd0558]
Event: 0.849 Thread 0x00000001318a8600 1535       1       java.util.ArrayDeque::peek (5 bytes)
Event: 0.849 Thread 0x00000001318a8600 nmethod 1535 0x000000010cbd0690 code [0x000000010cbd0800, 0x000000010cbd0918]
Event: 0.849 Thread 0x00000001318a8600 1536       1       java.util.ArrayDeque::peekFirst (12 bytes)
Event: 0.849 Thread 0x00000001318a8600 nmethod 1536 0x000000010cbd0a90 code [0x000000010cbd0c00, 0x000000010cbd0d18]
Event: 0.849 Thread 0x00000001318a8600 1537       1       org.springframework.boot.context.properties.source.SpringConfigurationPropertySources$SourcesIterator::fetchNext (138 bytes)
Event: 0.850 Thread 0x00000001318a8600 nmethod 1537 0x000000010cbd0e10 code [0x000000010cbd10c0, 0x000000010cbd1818]
Event: 0.855 Thread 0x00000001318a8600 1538       1       java.lang.Object::<init> (1 bytes)
Event: 0.855 Thread 0x00000001318a8600 nmethod 1538 0x000000010cbd1f90 code [0x000000010cbd2100, 0x000000010cbd21d8]
Event: 0.856 Thread 0x00000001318a8600 1542       1       org.jetbrains.capture.org.objectweb.asm.SymbolTable$Entry::<init> (17 bytes)
Event: 0.856 Thread 0x00000001318a8600 nmethod 1542 0x000000010cbd2590 code [0x000000010cbd2740, 0x000000010cbd2878]
Event: 0.856 Thread 0x00000001318a8600 1539       1       org.jetbrains.capture.org.objectweb.asm.SymbolTable::addConstantUtf8 (20 bytes)
Event: 0.856 Thread 0x00000001318a8600 nmethod 1539 0x000000010cbd2990 code [0x000000010cbd2b40, 0x000000010cbd2d38]
Event: 0.856 Thread 0x00000001318a8600 1540       1       org.jetbrains.capture.org.objectweb.asm.ClassReader::readClass (7 bytes)
Event: 0.856 Thread 0x00000001318a8600 nmethod 1540 0x000000010cbd2f90 code [0x000000010cbd3140, 0x000000010cbd33b8]
Event: 0.856 Thread 0x00000001318a8600 1541       1       org.jetbrains.capture.org.objectweb.asm.ClassReader::readStringish (16 bytes)
Event: 0.856 Thread 0x00000001318a8600 nmethod 1541 0x000000010cbd3690 code [0x000000010cbd3840, 0x000000010cbd3ab8]
Event: 0.857 Thread 0x00000001318a8600 1543       1       org.jetbrains.capture.org.objectweb.asm.SymbolTable::getMajorVersion (5 bytes)
Event: 0.857 Thread 0x00000001318a8600 nmethod 1543 0x000000010cbd3d90 code [0x000000010cbd3f00, 0x000000010cbd3f98]

GC Heap History (2 events):
Event: 0.628 GC heap before
{Heap before GC invocations=0 (full 0):
 garbage-first heap   total 266240K, used 22432K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 11 young (22528K), 0 survivors (0K)
 Metaspace       used 8125K, committed 8256K, reserved 1114112K
  class space    used 879K, committed 960K, reserved 1048576K
}
Event: 0.631 GC heap after
{Heap after GC invocations=1 (full 0):
 garbage-first heap   total 266240K, used 10638K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 8125K, committed 8256K, reserved 1114112K
  class space    used 879K, committed 960K, reserved 1048576K
}

Dll operation events (11 events):
Event: 0.268 Loaded shared library /Users/<USER>/Library/Java/JavaVirtualMachines/corretto-17.0.15/Contents/Home/lib/libjava.dylib
Event: 0.268 Loaded shared library /Users/<USER>/Library/Java/JavaVirtualMachines/corretto-17.0.15/Contents/Home/lib/libzip.dylib
Event: 0.326 Loaded shared library /Users/<USER>/Library/Java/JavaVirtualMachines/corretto-17.0.15/Contents/Home/lib/libinstrument.dylib
Event: 0.356 Loaded shared library /Users/<USER>/Library/Java/JavaVirtualMachines/corretto-17.0.15/Contents/Home/lib/libnio.dylib
Event: 0.358 Loaded shared library /Users/<USER>/Library/Java/JavaVirtualMachines/corretto-17.0.15/Contents/Home/lib/libjimage.dylib
Event: 0.373 Loaded shared library /Users/<USER>/Library/Java/JavaVirtualMachines/corretto-17.0.15/Contents/Home/lib/libzip.dylib
Event: 0.432 Loaded shared library /Users/<USER>/Library/Java/JavaVirtualMachines/corretto-17.0.15/Contents/Home/lib/libnet.dylib
Event: 0.445 Loaded shared library /Users/<USER>/Library/Java/JavaVirtualMachines/corretto-17.0.15/Contents/Home/lib/libmanagement.dylib
Event: 0.456 Loaded shared library /Users/<USER>/Library/Java/JavaVirtualMachines/corretto-17.0.15/Contents/Home/lib/libmanagement_ext.dylib
Event: 0.577 Loaded shared library /Users/<USER>/Library/Java/JavaVirtualMachines/corretto-17.0.15/Contents/Home/lib/libextnet.dylib
Event: 0.612 Loaded shared library /Users/<USER>/Library/Java/JavaVirtualMachines/corretto-17.0.15/Contents/Home/lib/libverify.dylib

Deoptimization events (20 events):
Event: 0.816 Thread 0x000000010c019c00 DEOPT PACKING pc=0x000000010cafe928 sp=0x000000016f898b50
Event: 0.816 Thread 0x000000010c019c00 DEOPT UNPACKING pc=0x000000010c84777c sp=0x000000016f8987f0 mode 1
Event: 0.816 Thread 0x000000010c019c00 DEOPT PACKING pc=0x000000010cafdabc sp=0x000000016f898c20
Event: 0.816 Thread 0x000000010c019c00 DEOPT UNPACKING pc=0x000000010c84777c sp=0x000000016f8988f0 mode 1
Event: 0.816 Thread 0x000000010c019c00 DEOPT PACKING pc=0x000000010caadb48 sp=0x000000016f8993d0
Event: 0.816 Thread 0x000000010c019c00 DEOPT UNPACKING pc=0x000000010c84777c sp=0x000000016f8990c0 mode 1
Event: 0.817 Thread 0x000000010c019c00 DEOPT PACKING pc=0x000000010cafe928 sp=0x000000016f898b50
Event: 0.817 Thread 0x000000010c019c00 DEOPT UNPACKING pc=0x000000010c84777c sp=0x000000016f8987f0 mode 1
Event: 0.817 Thread 0x000000010c019c00 DEOPT PACKING pc=0x000000010cafdabc sp=0x000000016f898c20
Event: 0.817 Thread 0x000000010c019c00 DEOPT UNPACKING pc=0x000000010c84777c sp=0x000000016f8988f0 mode 1
Event: 0.817 Thread 0x000000010c019c00 DEOPT PACKING pc=0x000000010caadb48 sp=0x000000016f8993d0
Event: 0.817 Thread 0x000000010c019c00 DEOPT UNPACKING pc=0x000000010c84777c sp=0x000000016f8990c0 mode 1
Event: 0.817 Thread 0x000000010c019c00 DEOPT PACKING pc=0x000000010cafe928 sp=0x000000016f898b50
Event: 0.817 Thread 0x000000010c019c00 DEOPT UNPACKING pc=0x000000010c84777c sp=0x000000016f8987f0 mode 1
Event: 0.817 Thread 0x000000010c019c00 DEOPT PACKING pc=0x000000010cafdabc sp=0x000000016f898c20
Event: 0.817 Thread 0x000000010c019c00 DEOPT UNPACKING pc=0x000000010c84777c sp=0x000000016f8988f0 mode 1
Event: 0.817 Thread 0x000000010c019c00 DEOPT PACKING pc=0x000000010caadb48 sp=0x000000016f8993d0
Event: 0.817 Thread 0x000000010c019c00 DEOPT UNPACKING pc=0x000000010c84777c sp=0x000000016f8990c0 mode 1
Event: 0.856 Thread 0x000000010c019c00 DEOPT PACKING pc=0x000000010ca61760 sp=0x000000016f8981c0
Event: 0.856 Thread 0x000000010c019c00 DEOPT UNPACKING pc=0x000000010c84777c sp=0x000000016f897f50 mode 3

Classes loaded (20 events):
Event: 0.857 Loading class java/util/concurrent/FutureTask done
Event: 0.857 Loading class java/util/concurrent/FutureTask$WaitNode
Event: 0.857 Loading class java/util/concurrent/FutureTask$WaitNode done
Event: 0.857 Loading class com/intellij/rt/debugger/agent/CaptureStorage$3
Event: 0.857 Loading class com/intellij/rt/debugger/agent/CaptureStorage$3 done
Event: 0.857 Loading class com/intellij/rt/debugger/agent/CaptureStorage$ExceptionCapturedStack
Event: 0.857 Loading class com/intellij/rt/debugger/agent/CaptureStorage$CapturedStack
Event: 0.857 Loading class com/intellij/rt/debugger/agent/CaptureStorage$CapturedStack done
Event: 0.857 Loading class com/intellij/rt/debugger/agent/CaptureStorage$ExceptionCapturedStack done
Event: 0.857 Loading class com/intellij/rt/debugger/agent/CaptureStorage$ConcurrentIdentityWeakHashMap$WeakKey
Event: 0.857 Loading class com/intellij/rt/debugger/agent/CaptureStorage$ConcurrentIdentityWeakHashMap$Key
Event: 0.857 Loading class com/intellij/rt/debugger/agent/CaptureStorage$ConcurrentIdentityWeakHashMap$Key done
Event: 0.857 Loading class com/intellij/rt/debugger/agent/CaptureStorage$ConcurrentIdentityWeakHashMap$WeakKey done
Event: 0.857 Loading class java/util/concurrent/ThreadPoolExecutor$Worker
Event: 0.857 Loading class java/util/concurrent/ThreadPoolExecutor$Worker done
Event: 0.858 Loading class com/intellij/rt/debugger/agent/CaptureStorage$5
Event: 0.858 Loading class com/intellij/rt/debugger/agent/CaptureStorage$5 done
Event: 0.858 Loading class com/intellij/rt/debugger/agent/CaptureStorage$ConcurrentIdentityWeakHashMap$HardKey
Event: 0.858 Loading class com/intellij/rt/debugger/agent/CaptureStorage$ConcurrentIdentityWeakHashMap$HardKey done
Event: 0.858 Loading class com/intellij/rt/debugger/agent/CaptureStorage$6

Classes unloaded (0 events):
No events

Classes redefined (1 events):
Event: 0.337 Thread 0x000000013178db10 redefined class name=java.lang.Throwable, count=1

Internal exceptions (20 events):
Event: 0.635 Thread 0x000000010c019c00 Exception <a 'java/lang/ClassNotFoundException'{0x000000070fe0ccd0}: javax/smartcardio/CardPermission> (0x000000070fe0ccd0) 
thrown [src/hotspot/share/classfile/systemDictionary.cpp, line 256]
Event: 0.636 Thread 0x000000010c019c00 Exception <a 'java/io/FileNotFoundException'{0x000000070fe1c9c8}> (0x000000070fe1c9c8) 
thrown [src/hotspot/share/prims/jni.cpp, line 516]
Event: 0.697 Thread 0x000000010c019c00 Exception <a 'java/lang/NoSuchMethodError'{0x000000070fb56da0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000070fb56da0) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 759]
Event: 0.704 Thread 0x000000010c019c00 Exception <a 'java/lang/NoSuchMethodError'{0x000000070f8140a0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x000000070f8140a0) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 759]
Event: 0.732 Thread 0x000000010c019c00 Exception <a 'java/lang/NoSuchMethodError'{0x000000070f716068}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000070f716068) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 759]
Event: 0.735 Thread 0x000000010c019c00 Exception <a 'java/lang/NoSuchMethodError'{0x000000070f787520}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000070f787520) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 759]
Event: 0.737 Thread 0x000000010c019c00 Exception <a 'java/lang/NoSuchMethodError'{0x000000070f7db650}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000070f7db650) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 759]
Event: 0.755 Thread 0x000000010c019c00 Exception <a 'java/lang/NoSuchMethodError'{0x000000070f213c90}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000070f213c90) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 759]
Event: 0.756 Thread 0x000000010c019c00 Exception <a 'java/lang/IncompatibleClassChangeError'{0x000000070f2170d0}: Found class java.lang.Object, but interface was expected> (0x000000070f2170d0) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 826]
Event: 0.763 Thread 0x000000010c019c00 Exception <a 'sun/nio/fs/UnixException'{0x000000070f2c4c90}> (0x000000070f2c4c90) 
thrown [src/hotspot/share/prims/jni.cpp, line 516]
Event: 0.802 Thread 0x000000010c019c00 Exception <a 'java/lang/NoSuchMethodError'{0x000000070ef47180}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000070ef47180) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 759]
Event: 0.810 Thread 0x000000010c019c00 Exception <a 'java/lang/NoSuchMethodError'{0x000000070ec31728}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000070ec31728) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 759]
Event: 0.818 Thread 0x000000010c019c00 Exception <a 'java/lang/NoSuchMethodError'{0x000000070ecd4f80}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000070ecd4f80) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 759]
Event: 0.818 Thread 0x000000010c019c00 Exception <a 'java/lang/NoSuchMethodError'{0x000000070ecd8920}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000070ecd8920) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 759]
Event: 0.818 Thread 0x000000010c019c00 Exception <a 'java/lang/NoSuchMethodError'{0x000000070ece6140}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000070ece6140) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 759]
Event: 0.820 Thread 0x000000010c019c00 Exception <a 'java/lang/IncompatibleClassChangeError'{0x000000070ecfc018}: Found class java.lang.Object, but interface was expected> (0x000000070ecfc018) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 826]
Event: 0.837 Thread 0x000000010c019c00 Exception <a 'java/lang/NoSuchMethodError'{0x000000070eb55cd0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000070eb55cd0) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 759]
Event: 0.840 Thread 0x000000010c019c00 Exception <a 'java/lang/NoSuchMethodError'{0x000000070eb9b898}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int)'> (0x000000070eb9b898) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 759]
Event: 0.840 Thread 0x000000010c019c00 Exception <a 'java/lang/NoSuchMethodError'{0x000000070eba7818}: 'void java.lang.invoke.DelegatingMethodHandle$Holder.delegate(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x000000070eba7818) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 759]
Event: 0.858 Thread 0x0000000131bb6400 Exception <a 'java/lang/NoClassDefFoundError'{0x000000070e4ee0a8}: com/intellij/rt/debugger/agent/CaptureStorage$ConcurrentIdentityWeakHashMap$HardKey> (0x000000070e4ee0a8) 
thrown [src/hotspot/share/classfile/systemDictionary.cpp, line 254]

VM Operations (20 events):
Event: 0.434 Executing VM operation: HandshakeAllThreads
Event: 0.434 Executing VM operation: HandshakeAllThreads done
Event: 0.466 Executing VM operation: HandshakeAllThreads
Event: 0.466 Executing VM operation: HandshakeAllThreads done
Event: 0.466 Executing VM operation: HandshakeAllThreads
Event: 0.466 Executing VM operation: HandshakeAllThreads done
Event: 0.541 Executing VM operation: HandshakeAllThreads
Event: 0.541 Executing VM operation: HandshakeAllThreads done
Event: 0.546 Executing VM operation: HandshakeAllThreads
Event: 0.546 Executing VM operation: HandshakeAllThreads done
Event: 0.570 Executing VM operation: HandshakeAllThreads
Event: 0.570 Executing VM operation: HandshakeAllThreads done
Event: 0.628 Executing VM operation: G1CollectForAllocation
Event: 0.631 Executing VM operation: G1CollectForAllocation done
Event: 0.694 Executing VM operation: HandshakeAllThreads
Event: 0.694 Executing VM operation: HandshakeAllThreads done
Event: 0.835 Executing VM operation: ICBufferFull
Event: 0.835 Executing VM operation: ICBufferFull done
Event: 0.850 Executing VM operation: HandshakeAllThreads
Event: 0.850 Executing VM operation: HandshakeAllThreads done

Memory protections (16 events):
Event: 0.267 Protecting memory [0x000000016f698000,0x000000016f6a4000] with protection modes 0
Event: 0.282 Protecting memory [0x0000000300628000,0x0000000300634000] with protection modes 0
Event: 0.282 Protecting memory [0x0000000300834000,0x0000000300840000] with protection modes 0
Event: 0.287 Protecting memory [0x0000000300a40000,0x0000000300a4c000] with protection modes 0
Event: 0.288 Protecting memory [0x0000000300c4c000,0x0000000300c58000] with protection modes 0
Event: 0.288 Protecting memory [0x0000000300e58000,0x0000000300e64000] with protection modes 0
Event: 0.288 Protecting memory [0x0000000301064000,0x0000000301070000] with protection modes 0
Event: 0.288 Protecting memory [0x0000000301270000,0x000000030127c000] with protection modes 0
Event: 0.299 Protecting memory [0x000000030147c000,0x0000000301488000] with protection modes 0
Event: 0.317 Protecting memory [0x0000000301688000,0x0000000301694000] with protection modes 0
Event: 0.317 Protecting memory [0x0000000301894000,0x00000003018a0000] with protection modes 0
Event: 0.317 Protecting memory [0x0000000301aa0000,0x0000000301aac000] with protection modes 0
Event: 0.343 Protecting memory [0x0000000301cac000,0x0000000301cb8000] with protection modes 0
Event: 0.347 Protecting memory [0x0000000301eb8000,0x0000000301ec4000] with protection modes 0
Event: 0.586 Protecting memory [0x00000003020c4000,0x00000003020d0000] with protection modes 0
Event: 0.858 Protecting memory [0x000000030353c000,0x0000000303548000] with protection modes 0

Nmethod flushes (0 events):
No events

Events (16 events):
Event: 0.278 Thread 0x000000010c019c00 Thread added: 0x000000010c019c00
Event: 0.282 Thread 0x000000010c019c00 Thread added: 0x000000010b2be600
Event: 0.282 Thread 0x000000010c019c00 Thread added: 0x000000010b2bec00
Event: 0.287 Thread 0x000000010c019c00 Thread added: 0x0000000142877600
Event: 0.288 Thread 0x000000010c019c00 Thread added: 0x00000001318a4a00
Event: 0.288 Thread 0x000000010c019c00 Thread added: 0x00000001318a5000
Event: 0.288 Thread 0x000000010c019c00 Thread added: 0x00000001318a8600
Event: 0.288 Thread 0x000000010c019c00 Thread added: 0x000000010b2c3c00
Event: 0.299 Thread 0x000000010c019c00 Thread added: 0x00000001420c3000
Event: 0.317 Thread 0x000000010c019c00 Thread added: 0x000000010b8dc400
Event: 0.317 Thread 0x000000010c019c00 Thread added: 0x000000010b8dca00
Event: 0.317 Thread 0x000000010b8dc400 Thread added: 0x000000010b2c4200
Event: 0.343 Thread 0x000000010c019c00 Thread added: 0x00000001318a8000
Event: 0.347 Thread 0x000000010c019c00 Thread added: 0x0000000142871200
Event: 0.586 Thread 0x000000010c019c00 Thread added: 0x000000010bab1c00
Event: 0.857 Thread 0x000000010c019c00 Thread added: 0x0000000131bb6400


Dynamic libraries:
0x0000000100830000 	/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-17.0.15/Contents/Home/lib/libjli.dylib
0x00000001a5ef6000 	/System/Library/Frameworks/Cocoa.framework/Versions/A/Cocoa
0x000000018d6bd000 	/System/Library/Frameworks/AppKit.framework/Versions/C/AppKit
0x00000001909ac000 	/System/Library/Frameworks/CoreData.framework/Versions/A/CoreData
0x000000018ad45000 	/System/Library/Frameworks/Foundation.framework/Versions/C/Foundation
0x0000000197929000 	/usr/lib/libSystem.B.dylib
0x000000018eb4f000 	/System/Library/PrivateFrameworks/UIFoundation.framework/Versions/A/UIFoundation
0x0000000237c5f000 	/System/Library/PrivateFrameworks/CollectionViewCore.framework/Versions/A/CollectionViewCore
0x000000019ef1c000 	/System/Library/PrivateFrameworks/RemoteViewServices.framework/Versions/A/RemoteViewServices
0x00000001954bd000 	/System/Library/PrivateFrameworks/XCTTargetBootstrap.framework/Versions/A/XCTTargetBootstrap
0x000000019a0f3000 	/System/Library/PrivateFrameworks/InternationalSupport.framework/Versions/A/InternationalSupport
0x000000019a44a000 	/System/Library/PrivateFrameworks/UserActivity.framework/Versions/A/UserActivity
0x00000002666cf000 	/System/Library/PrivateFrameworks/UIIntelligenceSupport.framework/Versions/A/UIIntelligenceSupport
0x00000001f0645000 	/System/Library/Frameworks/SwiftUICore.framework/Versions/A/SwiftUICore
0x000000026b748000 	/System/Library/PrivateFrameworks/WritingTools.framework/Versions/A/WritingTools
0x000000026a790000 	/System/Library/PrivateFrameworks/WindowManagement.framework/Versions/A/WindowManagement
0x000000018a9a9000 	/System/Library/Frameworks/SystemConfiguration.framework/Versions/A/SystemConfiguration
0x0000000199557000 	/usr/lib/libspindump.dylib
0x000000018ed01000 	/System/Library/Frameworks/UniformTypeIdentifiers.framework/Versions/A/UniformTypeIdentifiers
0x0000000196d1b000 	/usr/lib/libbsm.0.dylib
0x0000000192f40000 	/usr/lib/libapp_launch_measurement.dylib
0x00000001922e9000 	/System/Library/PrivateFrameworks/CoreAnalytics.framework/Versions/A/CoreAnalytics
0x0000000192f44000 	/System/Library/PrivateFrameworks/CoreAutoLayout.framework/Versions/A/CoreAutoLayout
0x0000000194ad7000 	/System/Library/Frameworks/Metal.framework/Versions/A/Metal
0x0000000195cfb000 	/usr/lib/liblangid.dylib
0x00000001954c3000 	/System/Library/PrivateFrameworks/CoreSVG.framework/Versions/A/CoreSVG
0x000000018f727000 	/System/Library/PrivateFrameworks/SkyLight.framework/Versions/A/SkyLight
0x000000018fc4d000 	/System/Library/Frameworks/CoreGraphics.framework/Versions/A/CoreGraphics
0x000000019f5fb000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Accelerate
0x000000019939a000 	/System/Library/PrivateFrameworks/IconServices.framework/Versions/A/IconServices
0x0000000194ab4000 	/System/Library/Frameworks/IOSurface.framework/Versions/A/IOSurface
0x000000019231a000 	/usr/lib/libDiagnosticMessagesClient.dylib
0x0000000197873000 	/usr/lib/libz.1.dylib
0x00000001a350e000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/ApplicationServices
0x00000001954a8000 	/System/Library/PrivateFrameworks/DFRFoundation.framework/Versions/A/DFRFoundation
0x000000018cf22000 	/usr/lib/libicucore.A.dylib
0x000000019b4d1000 	/System/Library/Frameworks/AudioToolbox.framework/Versions/A/AudioToolbox
0x000000019a3fb000 	/System/Library/PrivateFrameworks/DataDetectorsCore.framework/Versions/A/DataDetectorsCore
0x00000001b6465000 	/System/Library/PrivateFrameworks/TextInput.framework/Versions/A/TextInput
0x000000018f672000 	/usr/lib/libMobileGestalt.dylib
0x00000001951a1000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/HIToolbox.framework/Versions/A/HIToolbox
0x0000000192821000 	/System/Library/Frameworks/QuartzCore.framework/Versions/A/QuartzCore
0x000000018cb17000 	/System/Library/Frameworks/Security.framework/Versions/A/Security
0x000000019ef58000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/SpeechRecognition.framework/Versions/A/SpeechRecognition
0x0000000192c47000 	/System/Library/PrivateFrameworks/CoreUI.framework/Versions/A/CoreUI
0x000000018c3e2000 	/System/Library/Frameworks/CoreAudio.framework/Versions/A/CoreAudio
0x0000000192408000 	/System/Library/Frameworks/DiskArbitration.framework/Versions/A/DiskArbitration
0x00000001999c0000 	/System/Library/PrivateFrameworks/MultitouchSupport.framework/Versions/A/MultitouchSupport
0x000000018f670000 	/usr/lib/libenergytrace.dylib
0x00000001aa80b000 	/System/Library/PrivateFrameworks/RenderBox.framework/Versions/A/RenderBox
0x000000018d56d000 	/System/Library/Frameworks/IOKit.framework/Versions/A/IOKit
0x000000019f34f000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/CoreServices
0x0000000192ed1000 	/System/Library/PrivateFrameworks/PerformanceAnalysis.framework/Versions/A/PerformanceAnalysis
0x00000001ea015000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/OpenGL
0x0000000192f8e000 	/usr/lib/libxml2.2.dylib
0x0000000196bff000 	/System/Library/PrivateFrameworks/MobileKeyBag.framework/Versions/A/MobileKeyBag
0x00000001892ec000 	/usr/lib/libobjc.A.dylib
0x00000001895f9000 	/usr/lib/libc++.1.dylib
0x000000019f2d0000 	/System/Library/Frameworks/Accessibility.framework/Versions/A/Accessibility
0x000000019037d000 	/System/Library/Frameworks/ColorSync.framework/Versions/A/ColorSync
0x0000000189755000 	/System/Library/Frameworks/CoreFoundation.framework/Versions/A/CoreFoundation
0x000000019587d000 	/System/Library/Frameworks/CoreImage.framework/Versions/A/CoreImage
0x000000018c1c3000 	/System/Library/Frameworks/CoreText.framework/Versions/A/CoreText
0x00000001eb55a000 	/System/Library/Frameworks/CoreTransferable.framework/Versions/A/CoreTransferable
0x00000001ebade000 	/System/Library/Frameworks/DataDetection.framework/Versions/A/DataDetection
0x00000001ebae1000 	/System/Library/Frameworks/DeveloperToolsSupport.framework/Versions/A/DeveloperToolsSupport
0x00000001954fe000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/ImageIO
0x00000001f1208000 	/System/Library/Frameworks/Symbols.framework/Versions/A/Symbols
0x00000001dd824000 	/System/Library/PrivateFrameworks/FeatureFlags.framework/Versions/A/FeatureFlags
0x000000019792e000 	/System/Library/PrivateFrameworks/SoftLinking.framework/Versions/A/SoftLinking
0x00000001c9138000 	/usr/lib/swift/libswiftAccelerate.dylib
0x000000019ae66000 	/usr/lib/swift/libswiftCore.dylib
0x00000001b2b0e000 	/usr/lib/swift/libswiftCoreFoundation.dylib
0x00000001b2b68000 	/usr/lib/swift/libswiftCoreImage.dylib
0x00000001aff22000 	/usr/lib/swift/libswiftDarwin.dylib
0x000000027235d000 	/usr/lib/swift/libswiftDataDetection.dylib
0x00000001a0e47000 	/usr/lib/swift/libswiftDispatch.dylib
0x00000001b2b69000 	/usr/lib/swift/libswiftIOKit.dylib
0x00000001bf6bf000 	/usr/lib/swift/libswiftMetal.dylib
0x00000001ce695000 	/usr/lib/swift/libswiftOSLog.dylib
0x00000001a3ab4000 	/usr/lib/swift/libswiftObjectiveC.dylib
0x000000027238a000 	/usr/lib/swift/libswiftObservation.dylib
0x00000001c4ae6000 	/usr/lib/swift/libswiftQuartzCore.dylib
0x00000001c9129000 	/usr/lib/swift/libswiftUniformTypeIdentifiers.dylib
0x00000001b2b20000 	/usr/lib/swift/libswiftXPC.dylib
0x0000000272470000 	/usr/lib/swift/libswift_Builtin_float.dylib
0x0000000272473000 	/usr/lib/swift/libswift_Concurrency.dylib
0x00000002725d2000 	/usr/lib/swift/libswift_StringProcessing.dylib
0x0000000272665000 	/usr/lib/swift/libswift_errno.dylib
0x0000000272667000 	/usr/lib/swift/libswift_math.dylib
0x000000027266a000 	/usr/lib/swift/libswift_signal.dylib
0x000000027266b000 	/usr/lib/swift/libswift_stdio.dylib
0x000000027266c000 	/usr/lib/swift/libswift_time.dylib
0x00000001a3ab8000 	/usr/lib/swift/libswiftos.dylib
0x00000001b63bc000 	/usr/lib/swift/libswiftsimd.dylib
0x000000027266d000 	/usr/lib/swift/libswiftsys_time.dylib
0x000000027266e000 	/usr/lib/swift/libswiftunistd.dylib
0x0000000197b56000 	/usr/lib/libcompression.dylib
0x000000019a053000 	/System/Library/PrivateFrameworks/TextureIO.framework/Versions/A/TextureIO
0x0000000199039000 	/usr/lib/libate.dylib
0x0000000197923000 	/usr/lib/system/libcache.dylib
0x00000001978de000 	/usr/lib/system/libcommonCrypto.dylib
0x0000000197909000 	/usr/lib/system/libcompiler_rt.dylib
0x00000001978fe000 	/usr/lib/system/libcopyfile.dylib
0x0000000189447000 	/usr/lib/system/libcorecrypto.dylib
0x000000018952d000 	/usr/lib/system/libdispatch.dylib
0x00000001896ed000 	/usr/lib/system/libdyld.dylib
0x0000000197919000 	/usr/lib/system/libkeymgr.dylib
0x00000001978c1000 	/usr/lib/system/libmacho.dylib
0x0000000196cf4000 	/usr/lib/system/libquarantine.dylib
0x0000000197916000 	/usr/lib/system/libremovefile.dylib
0x000000018f6ec000 	/usr/lib/system/libsystem_asl.dylib
0x00000001893dc000 	/usr/lib/system/libsystem_blocks.dylib
0x0000000189577000 	/usr/lib/system/libsystem_c.dylib
0x000000019790d000 	/usr/lib/system/libsystem_collections.dylib
0x0000000195ce8000 	/usr/lib/system/libsystem_configuration.dylib
0x0000000194a83000 	/usr/lib/system/libsystem_containermanager.dylib
0x0000000197404000 	/usr/lib/system/libsystem_coreservices.dylib
0x000000018d1ee000 	/usr/lib/system/libsystem_darwin.dylib
0x00000002727a5000 	/usr/lib/system/libsystem_darwindirectory.dylib
0x000000019791a000 	/usr/lib/system/libsystem_dnssd.dylib
0x00000002727a9000 	/usr/lib/system/libsystem_eligibility.dylib
0x0000000189574000 	/usr/lib/system/libsystem_featureflags.dylib
0x0000000189725000 	/usr/lib/system/libsystem_info.dylib
0x0000000197882000 	/usr/lib/system/libsystem_m.dylib
0x00000001894e6000 	/usr/lib/system/libsystem_malloc.dylib
0x000000018f655000 	/usr/lib/system/libsystem_networkextension.dylib
0x000000018d650000 	/usr/lib/system/libsystem_notify.dylib
0x0000000195ced000 	/usr/lib/system/libsystem_sandbox.dylib
0x00000002727b1000 	/usr/lib/system/libsystem_sanitizers.dylib
0x0000000197912000 	/usr/lib/system/libsystem_secinit.dylib
0x00000001896a4000 	/usr/lib/system/libsystem_kernel.dylib
0x000000018971d000 	/usr/lib/system/libsystem_platform.dylib
0x00000001896e0000 	/usr/lib/system/libsystem_pthread.dylib
0x0000000191246000 	/usr/lib/system/libsystem_symptoms.dylib
0x000000018942b000 	/usr/lib/system/libsystem_trace.dylib
0x00000001978ec000 	/usr/lib/system/libunwind.dylib
0x00000001893e0000 	/usr/lib/system/libxpc.dylib
0x0000000189686000 	/usr/lib/libc++abi.dylib
0x000000027137d000 	/usr/lib/libRosetta.dylib
0x000000018d4ec000 	/System/Library/PrivateFrameworks/CoreServicesInternal.framework/Versions/A/CoreServicesInternal
0x00000001978f6000 	/usr/lib/liboah.dylib
0x000000019792b000 	/usr/lib/libfakelink.dylib
0x00000001a2fc1000 	/System/Library/PrivateFrameworks/DiskImages.framework/Versions/A/DiskImages
0x00000001afa4b000 	/System/Library/Frameworks/NetFS.framework/Versions/A/NetFS
0x000000018f28a000 	/System/Library/Frameworks/CFNetwork.framework/Versions/A/CFNetwork
0x0000000192f08000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/FSEvents.framework/Versions/A/FSEvents
0x000000018d1f9000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/CarbonCore.framework/Versions/A/CarbonCore
0x000000019237d000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/Metadata.framework/Versions/A/Metadata
0x000000019740b000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/OSServices.framework/Versions/A/OSServices
0x0000000197a78000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SearchKit.framework/Versions/A/SearchKit
0x00000001911c1000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/AE.framework/Versions/A/AE
0x0000000189c94000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/LaunchServices.framework/Versions/A/LaunchServices
0x0000000198e8e000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/DictionaryServices.framework/Versions/A/DictionaryServices
0x0000000192f16000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SharedFileList.framework/Versions/A/SharedFileList
0x0000000197b0d000 	/usr/lib/libapple_nghttp2.dylib
0x0000000190e22000 	/usr/lib/libsqlite3.dylib
0x000000019bb49000 	/System/Library/Frameworks/GSS.framework/Versions/A/GSS
0x0000000191158000 	/System/Library/PrivateFrameworks/RunningBoardServices.framework/Versions/A/RunningBoardServices
0x000000019124f000 	/System/Library/Frameworks/Network.framework/Versions/A/Network
0x00000001978c5000 	/usr/lib/system/libkxld.dylib
0x0000000233819000 	/System/Library/PrivateFrameworks/AppleKeyStore.framework/Versions/A/AppleKeyStore
0x00000002711d5000 	/usr/lib/libCoreEntitlements.dylib
0x000000024ec55000 	/System/Library/PrivateFrameworks/MessageSecurity.framework/Versions/A/MessageSecurity
0x0000000190e07000 	/System/Library/PrivateFrameworks/ProtocolBuffer.framework/Versions/A/ProtocolBuffer
0x00000001973eb000 	/System/Library/PrivateFrameworks/AppleFSCompression.framework/Versions/A/AppleFSCompression
0x0000000196d03000 	/usr/lib/libcoretls.dylib
0x0000000198f04000 	/usr/lib/libcoretls_cfhelpers.dylib
0x0000000197b50000 	/usr/lib/libpam.2.dylib
0x0000000198f78000 	/usr/lib/libxar.1.dylib
0x0000000197980000 	/usr/lib/libarchive.2.dylib
0x000000019d24b000 	/System/Library/Frameworks/Combine.framework/Versions/A/Combine
0x0000000237c83000 	/System/Library/PrivateFrameworks/CollectionsInternal.framework/Versions/A/CollectionsInternal
0x0000000257c7c000 	/System/Library/PrivateFrameworks/ReflectionInternal.framework/Versions/A/ReflectionInternal
0x000000025894e000 	/System/Library/PrivateFrameworks/RuntimeInternal.framework/Versions/A/RuntimeInternal
0x0000000272401000 	/usr/lib/swift/libswiftSystem.dylib
0x0000000195cf6000 	/System/Library/PrivateFrameworks/AppleSystemInfo.framework/Versions/A/AppleSystemInfo
0x00000001b29f9000 	/System/Library/PrivateFrameworks/LoggingSupport.framework/Versions/A/LoggingSupport
0x00000001a0308000 	/System/Library/PrivateFrameworks/PowerLog.framework/Versions/A/PowerLog
0x00000001f050a000 	/System/Library/Frameworks/SwiftData.framework/Versions/A/SwiftData
0x0000000193119000 	/System/Library/PrivateFrameworks/UserManagement.framework/Versions/A/UserManagement
0x000000018f1b8000 	/usr/lib/libboringssl.dylib
0x0000000191235000 	/usr/lib/libdns_services.dylib
0x00000001b1bad000 	/usr/lib/libquic.dylib
0x000000019adf5000 	/usr/lib/libusrtcp.dylib
0x00000001d64ff000 	/System/Library/PrivateFrameworks/InternalSwiftProtobuf.framework/Versions/A/InternalSwiftProtobuf
0x000000027235e000 	/usr/lib/swift/libswiftDistributed.dylib
0x00000002723f7000 	/usr/lib/swift/libswiftSynchronization.dylib
0x000000018f289000 	/usr/lib/libnetwork.dylib
0x00000001c3725000 	/System/Library/PrivateFrameworks/OSAnalytics.framework/Versions/A/OSAnalytics
0x0000000197ae8000 	/System/Library/PrivateFrameworks/AppleSauce.framework/Versions/A/AppleSauce
0x00000001a15dd000 	/System/Library/PrivateFrameworks/CoreSymbolication.framework/Versions/A/CoreSymbolication
0x00000001d21cb000 	/System/Library/PrivateFrameworks/Symbolication.framework/Versions/A/Symbolication
0x00000001a15b9000 	/System/Library/PrivateFrameworks/DebugSymbols.framework/Versions/A/DebugSymbols
0x00000002710a0000 	/usr/lib/libAppleArchive.dylib
0x00000001973f7000 	/usr/lib/libbz2.1.0.dylib
0x0000000198ee5000 	/usr/lib/liblzma.5.dylib
0x00000001969f3000 	/System/Library/PrivateFrameworks/IOMobileFramebuffer.framework/Versions/A/IOMobileFramebuffer
0x00000001b2a82000 	/System/Library/PrivateFrameworks/MallocStackLogging.framework/Versions/A/MallocStackLogging
0x00000001990d3000 	/System/Library/PrivateFrameworks/CrashReporterSupport.framework/Versions/A/CrashReporterSupport
0x000000018a7a0000 	/System/Library/PrivateFrameworks/Lexicon.framework/Versions/A/Lexicon
0x0000000246fa5000 	/System/Library/PrivateFrameworks/IO80211.framework/Versions/A/IO80211
0x00000001a10df000 	/System/Library/PrivateFrameworks/FrontBoardServices.framework/Versions/A/FrontBoardServices
0x0000000196c2b000 	/System/Library/Frameworks/SecurityFoundation.framework/Versions/A/SecurityFoundation
0x0000000196d8d000 	/usr/lib/libgermantok.dylib
0x0000000195e1a000 	/System/Library/PrivateFrameworks/LinguisticData.framework/Versions/A/LinguisticData
0x0000000191073000 	/System/Library/PrivateFrameworks/BaseBoard.framework/Versions/A/BaseBoard
0x00000001a11ac000 	/System/Library/PrivateFrameworks/BoardServices.framework/Versions/A/BoardServices
0x0000000196c1c000 	/System/Library/PrivateFrameworks/AssertionServices.framework/Versions/A/AssertionServices
0x00000001a2dfc000 	/System/Library/PrivateFrameworks/BackBoardServices.framework/Versions/A/BackBoardServices
0x0000000194ece000 	/System/Library/PrivateFrameworks/FontServices.framework/libFontParser.dylib
0x000000018f704000 	/System/Library/PrivateFrameworks/TCC.framework/Versions/A/TCC
0x00000001a6997000 	/System/Library/PrivateFrameworks/IOSurfaceAccelerator.framework/Versions/A/IOSurfaceAccelerator
0x00000001999be000 	/System/Library/PrivateFrameworks/WatchdogClient.framework/Versions/A/WatchdogClient
0x000000018bcea000 	/System/Library/Frameworks/CoreDisplay.framework/Versions/A/CoreDisplay
0x0000000194d72000 	/System/Library/Frameworks/CoreMedia.framework/Versions/A/CoreMedia
0x0000000194acd000 	/System/Library/PrivateFrameworks/IOAccelerator.framework/Versions/A/IOAccelerator
0x0000000193078000 	/System/Library/Frameworks/CoreVideo.framework/Versions/A/CoreVideo
0x0000000197b4e000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/MetalPerformanceShaders
0x0000000256fcd000 	/System/Library/PrivateFrameworks/ProDisplayLibrary.framework/Versions/A/ProDisplayLibrary
0x0000000199a06000 	/System/Library/Frameworks/VideoToolbox.framework/Versions/A/VideoToolbox
0x0000000195cf4000 	/System/Library/PrivateFrameworks/AggregateDictionary.framework/Versions/A/AggregateDictionary
0x0000000198f06000 	/System/Library/PrivateFrameworks/APFS.framework/Versions/A/APFS
0x0000000198f87000 	/usr/lib/libutil.dylib
0x0000000261e8c000 	/System/Library/PrivateFrameworks/SwiftASN1Internal.framework/Versions/A/SwiftASN1Internal
0x0000000192411000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vImage.framework/Versions/A/vImage
0x000000019f32a000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/vecLib
0x0000000198fbf000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libvMisc.dylib
0x000000018a10c000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libBLAS.dylib
0x000000019b720000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/ATS
0x0000000190533000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/HIServices.framework/Versions/A/HIServices
0x0000000199fdc000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/PrintCore.framework/Versions/A/PrintCore
0x000000019bb13000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/QD.framework/Versions/A/QD
0x000000019bb0a000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ColorSyncLegacy.framework/Versions/A/ColorSyncLegacy
0x000000019b6f2000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/SpeechSynthesis.framework/Versions/A/SpeechSynthesis
0x00000001969c0000 	/System/Library/PrivateFrameworks/CoreEmoji.framework/Versions/A/CoreEmoji
0x000000023e07e000 	/System/Library/PrivateFrameworks/FontServices.framework/Versions/A/FontServices
0x000000019950c000 	/System/Library/PrivateFrameworks/OTSVG.framework/Versions/A/OTSVG
0x0000000192bf5000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/Resources/libFontRegistry.dylib
0x0000000271761000 	/usr/lib/libhvf.dylib
0x0000000252edb000 	/System/Library/PrivateFrameworks/ParsingInternal.framework/Versions/A/ParsingInternal
0x00000002723a7000 	/usr/lib/swift/libswiftRegexBuilder.dylib
0x000000027252f000 	/usr/lib/swift/libswift_RegexParser.dylib
0x0000000199887000 	/System/Library/PrivateFrameworks/AppleJPEG.framework/Versions/A/AppleJPEG
0x0000000199328000 	/usr/lib/libexpat.1.dylib
0x0000000199eb2000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libPng.dylib
0x0000000199edd000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libTIFF.dylib
0x0000000199fc7000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libGIF.dylib
0x00000001998cd000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libJP2.dylib
0x0000000199f6e000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libJPEG.dylib
0x0000000199f65000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libRadiance.dylib
0x0000000242d4c000 	/System/Library/PrivateFrameworks/GPUCompiler.framework/Versions/32023/Libraries/libllvm-flatbuffers.dylib
0x000000023e149000 	/System/Library/PrivateFrameworks/FramePacing.framework/Versions/A/FramePacing
0x00000001ea007000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCoreFSCache.dylib
0x000000023ecbe000 	/System/Library/PrivateFrameworks/GPUCompiler.framework/Versions/32023/Libraries/libGPUCompilerUtils.dylib
0x000000023e07f000 	/System/Library/PrivateFrameworks/FontServices.framework/libXTFontStaticRegistryData.dylib
0x00000001a4bfd000 	/System/Library/PrivateFrameworks/ASEProcessing.framework/Versions/A/ASEProcessing
0x000000025613c000 	/System/Library/PrivateFrameworks/PhotosensitivityProcessing.framework/Versions/A/PhotosensitivityProcessing
0x0000000199500000 	/System/Library/PrivateFrameworks/GraphVisualizer.framework/Versions/A/GraphVisualizer
0x00000001ea065000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLU.dylib
0x00000001ea029000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGFXShared.dylib
0x00000001ea1f1000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGL.dylib
0x00000001ea032000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLImage.dylib
0x00000001ea026000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCVMSPluginSupport.dylib
0x00000001ea00f000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCoreVMClient.dylib
0x0000000195c38000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSCore.framework/Versions/A/MPSCore
0x0000000197356000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSImage.framework/Versions/A/MPSImage
0x0000000196da5000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSNeuralNetwork.framework/Versions/A/MPSNeuralNetwork
0x00000001971dc000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSMatrix.framework/Versions/A/MPSMatrix
0x0000000196ff1000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSRayIntersector.framework/Versions/A/MPSRayIntersector
0x000000019720e000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSNDArray.framework/Versions/A/MPSNDArray
0x00000001ed8fc000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSFunctions.framework/Versions/A/MPSFunctions
0x00000001ed8de000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSBenchmarkLoop.framework/Versions/A/MPSBenchmarkLoop
0x0000000189f87000 	/System/Library/PrivateFrameworks/MetalTools.framework/Versions/A/MetalTools
0x00000001b7762000 	/System/Library/PrivateFrameworks/IOAccelMemoryInfo.framework/Versions/A/IOAccelMemoryInfo
0x00000001c4edb000 	/System/Library/PrivateFrameworks/kperf.framework/Versions/A/kperf
0x00000001b2ae9000 	/System/Library/PrivateFrameworks/GPURawCounter.framework/Versions/A/GPURawCounter
0x0000000199f99000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATSUI.framework/Versions/A/ATSUI
0x000000019ba9e000 	/usr/lib/libcups.2.dylib
0x000000019bb38000 	/System/Library/Frameworks/Kerberos.framework/Versions/A/Kerberos
0x000000019b79e000 	/usr/lib/libresolv.9.dylib
0x0000000197965000 	/usr/lib/libiconv.2.dylib
0x000000019955e000 	/System/Library/PrivateFrameworks/Heimdal.framework/Versions/A/Heimdal
0x00000001a3a0b000 	/System/Library/Frameworks/Kerberos.framework/Versions/A/Libraries/libHeimdalProxy.dylib
0x0000000199343000 	/usr/lib/libheimdal-asn1.dylib
0x0000000192edb000 	/System/Library/Frameworks/OpenDirectory.framework/Versions/A/OpenDirectory
0x000000019bb9b000 	/System/Library/PrivateFrameworks/CommonAuth.framework/Versions/A/CommonAuth
0x0000000192ee9000 	/System/Library/Frameworks/OpenDirectory.framework/Versions/A/Frameworks/CFOpenDirectory.framework/Versions/A/CFOpenDirectory
0x00000001978c0000 	/usr/lib/libcharset.1.dylib
0x00000001ea58d000 	/System/Library/Frameworks/AVFAudio.framework/Versions/A/AVFAudio
0x00000001ac1d1000 	/System/Library/PrivateFrameworks/AXCoreUtilities.framework/Versions/A/AXCoreUtilities
0x000000024a8d4000 	/System/Library/PrivateFrameworks/IsolatedCoreAudioClient.framework/Versions/A/IsolatedCoreAudioClient
0x000000019b6d9000 	/usr/lib/libAudioStatistics.dylib
0x0000000194d4a000 	/System/Library/PrivateFrameworks/caulk.framework/Versions/A/caulk
0x000000018be0b000 	/System/Library/PrivateFrameworks/AudioToolboxCore.framework/Versions/A/AudioToolboxCore
0x00000001a5d23000 	/System/Library/Frameworks/CoreMIDI.framework/Versions/A/CoreMIDI
0x000000019b67c000 	/System/Library/PrivateFrameworks/AudioSession.framework/Versions/A/AudioSession
0x000000019cfb3000 	/System/Library/Frameworks/IOBluetooth.framework/Versions/A/IOBluetooth
0x000000019942d000 	/System/Library/PrivateFrameworks/MediaExperience.framework/Versions/A/MediaExperience
0x0000000264947000 	/System/Library/PrivateFrameworks/Tightbeam.framework/Versions/A/Tightbeam
0x0000000238740000 	/System/Library/PrivateFrameworks/CoreAudioOrchestration.framework/Versions/A/CoreAudioOrchestration
0x00000001c97fb000 	/System/Library/PrivateFrameworks/AFKUser.framework/Versions/A/AFKUser
0x00000001b2aed000 	/usr/lib/swift/libswiftCoreAudio.dylib
0x000000019954d000 	/System/Library/PrivateFrameworks/AppServerSupport.framework/Versions/A/AppServerSupport
0x000000019bb1c000 	/System/Library/PrivateFrameworks/perfdata.framework/Versions/A/perfdata
0x00000001b1ca6000 	/System/Library/PrivateFrameworks/SystemPolicy.framework/Versions/A/SystemPolicy
0x000000019b9bd000 	/usr/lib/libSMC.dylib
0x0000000199e7c000 	/usr/lib/libAudioToolboxUtility.dylib
0x000000019bb2a000 	/usr/lib/libperfcheck.dylib
0x00000002345d6000 	/System/Library/PrivateFrameworks/AudioAnalytics.framework/Versions/A/AudioAnalytics
0x00000001d62c6000 	/System/Library/Frameworks/OSLog.framework/Versions/A/OSLog
0x00000001b2ab4000 	/usr/lib/libmis.dylib
0x000000019b48a000 	/System/Library/PrivateFrameworks/AudioSession.framework/libSessionUtility.dylib
0x0000000199fcd000 	/System/Library/PrivateFrameworks/CMCaptureCore.framework/Versions/A/CMCaptureCore
0x00000001ebc2e000 	/System/Library/Frameworks/ExtensionFoundation.framework/Versions/A/ExtensionFoundation
0x00000001a16ea000 	/System/Library/PrivateFrameworks/CoreTime.framework/Versions/A/CoreTime
0x0000000199227000 	/System/Library/PrivateFrameworks/PlugInKit.framework/Versions/A/PlugInKit
0x00000001a0211000 	/System/Library/Frameworks/CoreBluetooth.framework/Versions/A/CoreBluetooth
0x00000001a3a0c000 	/System/Library/Frameworks/AudioUnit.framework/Versions/A/AudioUnit
0x0000000196a84000 	/System/Library/PrivateFrameworks/CoreUtils.framework/Versions/A/CoreUtils
0x000000023b6e4000 	/System/Library/PrivateFrameworks/CoreUtilsExtras.framework/Versions/A/CoreUtilsExtras
0x000000019f64b000 	/System/Library/Frameworks/MediaAccessibility.framework/Versions/A/MediaAccessibility
0x00000001bb0fe000 	/System/Library/PrivateFrameworks/AttributeGraph.framework/Versions/A/AttributeGraph
0x000000027101e000 	/usr/lib/libAXSafeCategoryBundle.dylib
0x00000001ac28d000 	/usr/lib/libAccessibility.dylib
0x00000001968ce000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libvDSP.dylib
0x0000000197c2c000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libLAPACK.dylib
0x0000000196d90000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libLinearAlgebra.dylib
0x0000000197b27000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libSparseBLAS.dylib
0x0000000197c27000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libQuadrature.dylib
0x0000000195e21000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libBNNS.dylib
0x000000018a8ad000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libSparse.dylib
0x000000024c045000 	/System/Library/PrivateFrameworks/MIL.framework/Versions/A/MIL
0x0000000199f60000 	/System/Library/PrivateFrameworks/GPUWrangler.framework/Versions/A/GPUWrangler
0x0000000199f40000 	/System/Library/PrivateFrameworks/IOPresentment.framework/Versions/A/IOPresentment
0x0000000199f68000 	/System/Library/PrivateFrameworks/DSExternalDisplay.framework/Versions/A/DSExternalDisplay
0x00000001d66cd000 	/System/Library/PrivateFrameworks/HIDDisplay.framework/Versions/A/HIDDisplay
0x00000001ab4ac000 	/System/Library/PrivateFrameworks/HID.framework/Versions/A/HID
0x0000000268183000 	/System/Library/PrivateFrameworks/VideoToolboxParavirtualizationSupport.framework/Versions/A/VideoToolboxParavirtualizationSupport
0x00000001992df000 	/System/Library/PrivateFrameworks/AppleVA.framework/Versions/A/AppleVA
0x00000001a1259000 	/System/Library/PrivateFrameworks/GraphicsServices.framework/Versions/A/GraphicsServices
0x000000019bd0e000 	/System/Library/PrivateFrameworks/CorePhoneNumbers.framework/Versions/A/CorePhoneNumbers
0x00000001a03f9000 	/System/Library/PrivateFrameworks/MediaKit.framework/Versions/A/MediaKit
0x00000001a0344000 	/System/Library/Frameworks/DiscRecording.framework/Versions/A/DiscRecording
0x000000019bafd000 	/System/Library/PrivateFrameworks/NetAuth.framework/Versions/A/NetAuth
0x00000001930d9000 	/System/Library/PrivateFrameworks/login.framework/Versions/A/Frameworks/loginsupport.framework/Versions/A/loginsupport
0x000000019934d000 	/System/Library/PrivateFrameworks/IconFoundation.framework/Versions/A/IconFoundation
0x00000001990ca000 	/usr/lib/libIOReport.dylib
0x00000002341e6000 	/System/Library/PrivateFrameworks/AppleMobileFileIntegrity.framework/Versions/A/AppleMobileFileIntegrity
0x00000002713e0000 	/usr/lib/libTLE.dylib
0x00000001e7063000 	/System/Library/PrivateFrameworks/ConfigProfileHelper.framework/Versions/A/ConfigProfileHelper
0x0000000196d2d000 	/usr/lib/libmecab.dylib
0x000000018aa3d000 	/usr/lib/libCRFSuite.dylib
0x0000000195cfd000 	/System/Library/PrivateFrameworks/CoreNLP.framework/Versions/A/CoreNLP
0x0000000197ae0000 	/usr/lib/libThaiTokenizer.dylib
0x0000000196cf7000 	/usr/lib/libCheckFix.dylib
0x000000019231c000 	/System/Library/PrivateFrameworks/MetadataUtilities.framework/Versions/A/MetadataUtilities
0x0000000247887000 	/System/Library/PrivateFrameworks/InstalledContentLibrary.framework/Versions/A/InstalledContentLibrary
0x000000018d52c000 	/System/Library/PrivateFrameworks/CoreServicesStore.framework/Versions/A/CoreServicesStore
0x00000001c4fe5000 	/System/Library/PrivateFrameworks/MobileSystemServices.framework/Versions/A/MobileSystemServices
0x0000000198f8b000 	/usr/lib/libxslt.1.dylib
0x0000000196cba000 	/System/Library/PrivateFrameworks/BackgroundTaskManagement.framework/Versions/A/BackgroundTaskManagement
0x00000001a317f000 	/usr/lib/libcurl.4.dylib
0x0000000271614000 	/usr/lib/libcrypto.46.dylib
0x000000027213c000 	/usr/lib/libssl.48.dylib
0x00000001a2e58000 	/System/Library/Frameworks/LDAP.framework/Versions/A/LDAP
0x00000001a2e94000 	/System/Library/PrivateFrameworks/TrustEvaluationAgent.framework/Versions/A/TrustEvaluationAgent
0x000000019b7bb000 	/usr/lib/libsasl2.2.dylib
0x00000001aff21000 	/usr/lib/swift/libswiftCoreGraphics.dylib
0x000000019fc79000 	/usr/lib/swift/libswiftFoundation.dylib
0x00000001e6aed000 	/usr/lib/swift/libswiftSwiftOnoneSupport.dylib
0x00000001d297f000 	/System/Library/PrivateFrameworks/CoreMaterial.framework/Versions/A/CoreMaterial
0x0000000258a45000 	/System/Library/PrivateFrameworks/SFSymbols.framework/Versions/A/SFSymbols
0x000000019ef45000 	/System/Library/PrivateFrameworks/SpeechRecognitionCore.framework/Versions/A/SpeechRecognitionCore
0x0000000101d78000 	/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-17.0.15/Contents/Home/lib/server/libjvm.dylib
0x0000000100898000 	/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-17.0.15/Contents/Home/lib/libjimage.dylib
0x00000001008f4000 	/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-17.0.15/Contents/Home/lib/libjdwp.dylib
0x000000010093c000 	/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-17.0.15/Contents/Home/lib/libjava.dylib
0x00000001008c4000 	/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-17.0.15/Contents/Home/lib/libinstrument.dylib
0x0000000100ab4000 	/private/var/folders/83/lx4p4bhs5bqg28rs3x6_qp600000gn/T/idea_libasyncProfiler_dylib_temp_folder/libasyncProfiler.dylib
0x00000001009d8000 	/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-17.0.15/Contents/Home/lib/libzip.dylib
0x0000000271010000 	/usr/lib/i18n/libiconv_std.dylib
0x0000000271006000 	/usr/lib/i18n/libUTF8.dylib
0x0000000271015000 	/usr/lib/i18n/libmapper_none.dylib
0x0000000100a60000 	/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-17.0.15/Contents/Home/lib/libdt_socket.dylib
0x0000000100a74000 	/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-17.0.15/Contents/Home/lib/libnio.dylib
0x0000000100be0000 	/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-17.0.15/Contents/Home/lib/libnet.dylib
0x0000000100a94000 	/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-17.0.15/Contents/Home/lib/libmanagement.dylib
0x0000000100c04000 	/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-17.0.15/Contents/Home/lib/libmanagement_ext.dylib
0x0000000100c18000 	/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-17.0.15/Contents/Home/lib/libextnet.dylib
0x0000000100c2c000 	/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-17.0.15/Contents/Home/lib/libverify.dylib


VM Arguments:
jvm_args: -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:57629,suspend=y,server=n -javaagent:/Users/<USER>/Library/Caches/JetBrains/IntelliJIdea2025.1/captureAgent/debugger-agent.jar=file:///var/folders/83/lx4p4bhs5bqg28rs3x6_qp600000gn/T/capture6868567008683401803.props --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.time=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.math=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.security=ALL-UNNAMED --add-opens=java.base/java.text=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.base/java.util.regex=ALL-UNNAMED --add-opens=java.sql/java.sql=ALL-UNNAMED -agentpath:/private/var/folders/83/lx4p4bhs5bqg28rs3x6_qp600000gn/T/idea_libasyncProfiler_dylib_temp_folder/libasyncProfiler.dylib=version,jfr,event=wall,interval=10ms,cstack=no,file=/Users/<USER>/IdeaSnapshots/DelayedServiceApplication_2025_07_31_134249.jfr,log=/private/var/folders/83/lx4p4bhs5bqg28rs3x6_qp600000gn/T/DelayedServiceApplication_2025_07_31_134249.jfr.log.txt,logLevel=DEBUG -XX:TieredStopAtLevel=1 -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true -Dmanagement.endpoints.jmx.exposure.include=* -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Ddebugger.agent.enable.coroutines=true -Dkotlinx.coroutines.debug.enable.flows.stack.trace=true -Dkotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true -Dfile.encoding=UTF-8 
java_command: com.knet.delayed.DelayedServiceApplication
java_class_path (initial): /Users/<USER>/IdeaProjects/knet/delayed-services/target/classes:/Users/<USER>/IdeaProjects/knet/common/target/classes:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/2.7.18/spring-boot-starter-validation-2.7.18.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/9.0.83/tomcat-embed-el-9.0.83.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.2.5.Final/hibernate-validator-6.2.5.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.4.3.Final/jboss-logging-3.4.3.Final.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar:/Users/<USER>/.m2/repository/com/alibaba/cloud/spring-cloud-starter-alibaba-nacos-discovery/2021.0.5.0/spring-cloud-starter-alibaba-nacos-discovery-2021.0.5.0.jar:/Users/<USER>/.m2/repository/com/alibaba/cloud/spring-cloud-alibaba-commons/2021.0.5.0/spring-cloud-alibaba-commons-2021.0.5.0.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.2.0/nacos-client-2.2.0.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-auth-plugin/2.2.0/nacos-auth-plugin-2.2.0.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-encryption-plugin/2.2.0/nacos-encryption-plugin-2.2.0.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.15/commons-codec-1.15.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.13.5/jackson-core-2.13.5.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.13.5/jackson-databind-2.13.5.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.13.5/jackson-annotations-2.13.5.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpasyncclient/4.1.5/httpasyncclient-4.1.5.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.jar:/Users/<USER>/.m2/repository/
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 9                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 268435456                                 {product} {ergonomic}
     bool ManagementServer                         = true                                      {product} {command line}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4294967296                                {product} {ergonomic}
   size_t MaxNewSize                               = 2575302656                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 0                                      {pd product} {ergonomic}
     bool ProfileInterpreter                       = false                                  {pd product} {command line}
    uintx ProfiledCodeHeapSize                     = 0                                      {pd product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4294967296                             {manageable} {ergonomic}
     intx TieredStopAtLevel                        = 1                                         {product} {command line}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseNUMA                                  = false                                     {product} {ergonomic}
     bool UseNUMAInterleaving                      = false                                     {product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=/Users/<USER>/.jenv/versions/17.0.15
PATH=/Users/<USER>/.jenv/shims:/Users/<USER>/.jenv/bin:/Users/<USER>/dev/apache-maven-3.9.9/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin
SHELL=/bin/zsh
LC_CTYPE=zh_CN.UTF-8
TMPDIR=/var/folders/83/lx4p4bhs5bqg28rs3x6_qp600000gn/T/

Active Locale:
LC_ALL=C/zh_CN.UTF-8/C/C/C/C
LC_COLLATE=C
LC_CTYPE=zh_CN.UTF-8
LC_MESSAGES=C
LC_MONETARY=C
LC_NUMERIC=C
LC_TIME=C

Signal Handlers:
   SIGSEGV: crash_handler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, unblocked
    SIGBUS: crash_handler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, unblocked
    SIGFPE: crash_handler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, unblocked
   SIGPIPE: javaSignalHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
   SIGXFSZ: javaSignalHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
    SIGILL: crash_handler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, unblocked
   SIGUSR2: SR_handler in libjvm.dylib, mask=00000000000000000000000000000000, flags=SA_RESTART|SA_SIGINFO, blocked
    SIGHUP: UserHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
    SIGINT: UserHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
   SIGTERM: UserHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
   SIGQUIT: UserHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
   SIGTRAP: crash_handler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, unblocked


Periodic native trim disabled


---------------  S Y S T E M  ---------------

OS:
uname: Darwin 24.5.0 Darwin Kernel Version 24.5.0: Tue Apr 22 19:54:49 PDT 2025; root:xnu-11417.121.6~2/RELEASE_ARM64_T6000 arm64
OS uptime: 51 days 3:25 hours
rlimit (soft/hard): STACK 8176k/65520k , CORE 0k/infinity , NPROC 2666/4000 , NOFILE 1048576/infinity , AS infinity/infinity , CPU infinity/infinity , DATA infinity/infinity , FSIZE infinity/infinity , MEMLOCK infinity/infinity , RSS infinity/infinity
load average: 5.15 3.89 3.25

CPU: total 10 (initial active 10) 0x61:0x0:0x1b588bb3:0, fp, simd, crc, lse
machdep.cpu.brand_string:Apple M1 Pro
hw.cachelinesize:128
hw.l1icachesize:131072
hw.l1dcachesize:65536
hw.l2cachesize:4194304

Memory: 16k page, physical 16777216k(163184k free), swap 7340032k(1696640k free)

vm_info: OpenJDK 64-Bit Server VM (17.0.15+6-LTS) for bsd-aarch64 JRE (17.0.15+6-LTS), built on Apr 10 2025 00:09:34 by "ec2user" with clang Apple LLVM 14.0.0 (clang-1400.0.29.202)

END.
